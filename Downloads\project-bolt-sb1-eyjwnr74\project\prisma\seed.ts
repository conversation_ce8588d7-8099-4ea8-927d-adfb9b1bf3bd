import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Check if demo user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  })

  if (existingUser) {
    console.log('✅ <NAME_EMAIL> already exists')
    return
  }

  // Hash the demo password
  const hashedPassword = await bcrypt.hash('password123', 12)

  // Create demo store first
  const demoStore = await prisma.store.create({
    data: {
      name: 'Demo Grocery Store',
      address: '123 Main Street, Demo City',
      phone: '******-0123',
      email: '<EMAIL>',
      gstNumber: 'GST123456789',
      isActive: true,
      createdBy: {
        create: {
          name: 'Demo Admin',
          email: '<EMAIL>',
          password: hashedPassword,
          phone: '******-0100',
          role: 'SUPER_ADMIN',
          isActive: true
        }
      }
    },
    include: {
      createdBy: true
    }
  })

  // Update the user to be associated with the store
  await prisma.user.update({
    where: { id: demoStore.createdById },
    data: {
      storeId: demoStore.id
    }
  })

  // Create some demo categories
  const categories = await Promise.all([
    prisma.category.create({
      data: {
        name: 'Fruits & Vegetables',
        description: 'Fresh fruits and vegetables',
        storeId: demoStore.id,
        isActive: true
      }
    }),
    prisma.category.create({
      data: {
        name: 'Dairy Products',
        description: 'Milk, cheese, yogurt and other dairy items',
        storeId: demoStore.id,
        isActive: true
      }
    }),
    prisma.category.create({
      data: {
        name: 'Beverages',
        description: 'Soft drinks, juices, and other beverages',
        storeId: demoStore.id,
        isActive: true
      }
    })
  ])

  // Create some demo products
  const products = await Promise.all([
    prisma.product.create({
      data: {
        name: 'Fresh Apples',
        sku: 'FRUIT-001',
        description: 'Fresh red apples',
        unit: 'kg',
        costPrice: 2.50,
        sellingPrice: 3.99,
        mrp: 4.50,
        taxRate: 5.0,
        minStock: 10,
        categoryId: categories[0].id,
        storeId: demoStore.id,
        isActive: true
      }
    }),
    prisma.product.create({
      data: {
        name: 'Whole Milk',
        sku: 'DAIRY-001',
        description: 'Fresh whole milk 1L',
        unit: 'ltr',
        costPrice: 1.20,
        sellingPrice: 1.99,
        mrp: 2.25,
        taxRate: 5.0,
        minStock: 20,
        categoryId: categories[1].id,
        storeId: demoStore.id,
        isActive: true
      }
    }),
    prisma.product.create({
      data: {
        name: 'Orange Juice',
        sku: 'BEV-001',
        description: 'Fresh orange juice 500ml',
        unit: 'bottle',
        costPrice: 1.50,
        sellingPrice: 2.49,
        mrp: 2.99,
        taxRate: 12.0,
        minStock: 15,
        categoryId: categories[2].id,
        storeId: demoStore.id,
        isActive: true
      }
    })
  ])

  // Create inventory records for the products
  await Promise.all(
    products.map(product =>
      prisma.inventory.create({
        data: {
          productId: product.id,
          storeId: demoStore.id,
          quantity: 50,
          reorderLevel: product.minStock
        }
      })
    )
  )

  console.log('✅ Demo user created successfully!')
  console.log('📧 Email: <EMAIL>')
  console.log('🔑 Password: password123')
  console.log('🏪 Store: Demo Grocery Store')
  console.log('📦 Created 3 categories and 3 products with inventory')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
