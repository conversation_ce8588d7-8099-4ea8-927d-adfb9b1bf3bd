import { NextRequest } from 'next/server'
import { 
  withPermission, 
  createSuccessResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/users/stats - Get user statistics
export const GET = withPermission('USER', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)

  const [totalUsers, activeUsers, usersByRole] = await Promise.all([
    prisma.user.count({ where: { storeId } }),
    prisma.user.count({ where: { storeId, isActive: true } }),
    prisma.user.groupBy({
      by: ['role'],
      where: { storeId },
      _count: { role: true }
    })
  ])

  const roleStats = usersByRole.reduce((acc: any, item) => {
    acc[item.role] = item._count.role
    return acc
  }, {})

  const statistics = {
    totalUsers,
    activeUsers,
    inactiveUsers: totalUsers - activeUsers,
    roleDistribution: roleStats,
    recentActivity: await prisma.user.findMany({
      where: { storeId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })
  }

  return createSuccessResponse(statistics)
})
