import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/dashboard/insights - Get comprehensive business insights for dashboard
export const GET = withPermission('DASHBOARD', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const period = searchParams.get('period') || '30' // days
  const includeForecasts = searchParams.get('forecasts') === 'true'
  const includeAlerts = searchParams.get('alerts') === 'true'

  const periodDays = parseInt(period)
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - periodDays)

  try {
    const [
      kpiMetrics,
      revenueInsights,
      inventoryInsights,
      customerInsights,
      operationalInsights,
      alerts,
      forecasts
    ] = await Promise.all([
      // Key Performance Indicators
      getKPIMetrics(storeId, startDate),
      
      // Revenue Insights
      getRevenueInsights(storeId, startDate),
      
      // Inventory Insights
      getInventoryInsights(storeId, startDate),
      
      // Customer Insights
      getCustomerInsights(storeId, startDate),
      
      // Operational Insights
      getOperationalInsights(storeId, startDate),
      
      // Business Alerts (if requested)
      includeAlerts ? getBusinessAlerts(storeId) : null,
      
      // Forecasts (if requested)
      includeForecasts ? getBusinessForecasts(storeId, startDate) : null
    ])

    const insights = {
      period: {
        days: periodDays,
        startDate,
        endDate: new Date()
      },
      kpi: kpiMetrics,
      revenue: revenueInsights,
      inventory: inventoryInsights,
      customers: customerInsights,
      operations: operationalInsights,
      ...(alerts && { alerts }),
      ...(forecasts && { forecasts }),
      generatedAt: new Date()
    }

    return createSuccessResponse(insights, 'Business insights generated successfully')

  } catch (error) {
    console.error('Error generating business insights:', error)
    return createErrorResponse('Failed to generate business insights', 500)
  }
})

// Helper functions for different insight categories

async function getKPIMetrics(storeId: string, startDate: Date) {
  const [currentPeriod, previousPeriod] = await Promise.all([
    // Current period metrics
    Promise.all([
      // Revenue
      prisma.sale.aggregate({
        where: {
          storeId,
          createdAt: { gte: startDate },
          status: 'COMPLETED'
        },
        _sum: { total: true },
        _count: true
      }),
      
      // Profit (simplified calculation)
      prisma.saleItem.findMany({
        where: {
          sale: {
            storeId,
            createdAt: { gte: startDate },
            status: 'COMPLETED'
          }
        },
        include: {
          product: {
            select: { costPrice: true }
          }
        }
      }).then(items => {
        return items.reduce((profit, item) => {
          const cost = item.quantity * item.product.costPrice
          return profit + (item.totalPrice - cost)
        }, 0)
      }),
      
      // New customers
      prisma.customer.count({
        where: {
          storeId,
          createdAt: { gte: startDate }
        }
      }),
      
      // Inventory value
      prisma.inventory.findMany({
        where: { storeId },
        include: {
          product: {
            select: { costPrice: true }
          }
        }
      }).then(items => 
        items.reduce((total, item) => total + (item.quantity * item.product.costPrice), 0)
      )
    ]),
    
    // Previous period for comparison
    (() => {
      const prevStartDate = new Date(startDate)
      const daysDiff = Math.floor((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      prevStartDate.setDate(prevStartDate.getDate() - daysDiff)
      
      return Promise.all([
        prisma.sale.aggregate({
          where: {
            storeId,
            createdAt: { gte: prevStartDate, lt: startDate },
            status: 'COMPLETED'
          },
          _sum: { total: true },
          _count: true
        }),
        
        prisma.customer.count({
          where: {
            storeId,
            createdAt: { gte: prevStartDate, lt: startDate }
          }
        })
      ])
    })()
  ])

  const [revenue, profit, newCustomers, inventoryValue] = currentPeriod
  const [prevRevenue, prevNewCustomers] = previousPeriod

  return {
    revenue: {
      current: revenue._sum.total || 0,
      previous: prevRevenue._sum.total || 0,
      growth: calculateGrowthPercentage(revenue._sum.total || 0, prevRevenue._sum.total || 0)
    },
    profit: {
      current: profit,
      margin: revenue._sum.total ? (profit / revenue._sum.total) * 100 : 0
    },
    transactions: {
      current: revenue._count,
      previous: prevRevenue._count,
      growth: calculateGrowthPercentage(revenue._count, prevRevenue._count)
    },
    customers: {
      new: newCustomers,
      previous: prevNewCustomers,
      growth: calculateGrowthPercentage(newCustomers, prevNewCustomers)
    },
    inventory: {
      value: inventoryValue
    },
    averageOrderValue: {
      current: revenue._count > 0 ? (revenue._sum.total || 0) / revenue._count : 0,
      previous: prevRevenue._count > 0 ? (prevRevenue._sum.total || 0) / prevRevenue._count : 0
    }
  }
}

async function getRevenueInsights(storeId: string, startDate: Date) {
  const [salesTrends, topProducts, paymentMethods, hourlyPattern] = await Promise.all([
    // Daily sales trends
    prisma.sale.findMany({
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED'
      },
      select: {
        createdAt: true,
        total: true
      }
    }).then(sales => {
      const dailyData = new Map()
      sales.forEach(sale => {
        const date = sale.createdAt.toISOString().split('T')[0]
        const existing = dailyData.get(date) || { sales: 0, revenue: 0 }
        existing.sales += 1
        existing.revenue += sale.total
        dailyData.set(date, existing)
      })
      return Array.from(dailyData.entries()).map(([date, data]) => ({ date, ...data }))
    }),
    
    // Top revenue products
    prisma.saleItem.groupBy({
      by: ['productId'],
      where: {
        sale: {
          storeId,
          createdAt: { gte: startDate },
          status: 'COMPLETED'
        }
      },
      _sum: { totalPrice: true, quantity: true },
      orderBy: { _sum: { totalPrice: 'desc' } },
      take: 5
    }).then(async (items) => {
      const productIds = items.map(item => item.productId)
      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
        select: { id: true, name: true, sku: true }
      })
      
      return items.map(item => ({
        ...item,
        product: products.find(p => p.id === item.productId)
      }))
    }),
    
    // Payment method distribution
    prisma.payment.groupBy({
      by: ['method'],
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED'
      },
      _sum: { amount: true },
      _count: true
    }),
    
    // Hourly sales pattern
    prisma.sale.findMany({
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED'
      },
      select: {
        createdAt: true,
        total: true
      }
    }).then(sales => {
      const hourlyData = Array.from({ length: 24 }, (_, hour) => ({
        hour,
        sales: 0,
        revenue: 0
      }))
      
      sales.forEach(sale => {
        const hour = new Date(sale.createdAt).getHours()
        hourlyData[hour].sales += 1
        hourlyData[hour].revenue += sale.total
      })
      
      return hourlyData
    })
  ])

  return {
    trends: salesTrends,
    topProducts,
    paymentMethods,
    hourlyPattern,
    insights: generateRevenueInsights(salesTrends, topProducts, paymentMethods)
  }
}

async function getInventoryInsights(storeId: string, startDate: Date) {
  const [stockLevels, turnoverAnalysis, lowStockAlerts, categoryPerformance] = await Promise.all([
    // Stock level analysis
    prisma.inventory.findMany({
      where: { storeId },
      include: {
        product: {
          select: {
            name: true,
            costPrice: true,
            sellingPrice: true,
            category: {
              select: { name: true }
            }
          }
        }
      }
    }).then(items => {
      const analysis = {
        totalItems: items.length,
        totalValue: 0,
        outOfStock: 0,
        lowStock: 0,
        overStock: 0
      }
      
      items.forEach(item => {
        analysis.totalValue += item.quantity * item.product.costPrice
        
        if (item.quantity === 0) {
          analysis.outOfStock += 1
        } else if (item.quantity <= item.reorderLevel) {
          analysis.lowStock += 1
        } else if (item.quantity > item.maxStock) {
          analysis.overStock += 1
        }
      })
      
      return analysis
    }),
    
    // Inventory turnover
    Promise.all([
      prisma.inventory.findMany({
        where: { storeId },
        select: {
          productId: true,
          quantity: true,
          product: {
            select: { name: true, costPrice: true }
          }
        }
      }),
      
      prisma.saleItem.groupBy({
        by: ['productId'],
        where: {
          sale: {
            storeId,
            createdAt: { gte: startDate },
            status: 'COMPLETED'
          }
        },
        _sum: { quantity: true }
      })
    ]).then(([inventory, sales]) => {
      return inventory.map(inv => {
        const sold = sales.find(s => s.productId === inv.productId)?._sum.quantity || 0
        const turnover = inv.quantity > 0 ? sold / inv.quantity : 0
        
        return {
          productId: inv.productId,
          productName: inv.product.name,
          currentStock: inv.quantity,
          soldQuantity: sold,
          turnoverRate: turnover,
          stockValue: inv.quantity * inv.product.costPrice
        }
      }).sort((a, b) => b.turnoverRate - a.turnoverRate)
    }),
    
    // Low stock alerts
    prisma.inventory.findMany({
      where: {
        storeId,
        quantity: { lte: prisma.inventory.fields.reorderLevel }
      },
      include: {
        product: {
          select: { name: true, sku: true }
        }
      },
      take: 10
    }),
    
    // Category performance
    prisma.saleItem.findMany({
      where: {
        sale: {
          storeId,
          createdAt: { gte: startDate },
          status: 'COMPLETED'
        }
      },
      include: {
        product: {
          include: {
            category: {
              select: { name: true }
            }
          }
        }
      }
    }).then(items => {
      const categoryMap = new Map()
      
      items.forEach(item => {
        const categoryName = item.product.category.name
        const existing = categoryMap.get(categoryName) || {
          revenue: 0,
          quantity: 0,
          items: 0
        }
        
        existing.revenue += item.totalPrice
        existing.quantity += item.quantity
        existing.items += 1
        
        categoryMap.set(categoryName, existing)
      })
      
      return Array.from(categoryMap.entries()).map(([category, data]) => ({
        category,
        ...data
      })).sort((a, b) => b.revenue - a.revenue)
    })
  ])

  return {
    stockLevels,
    turnover: {
      fastMoving: turnoverAnalysis.filter(item => item.turnoverRate > 2).slice(0, 5),
      slowMoving: turnoverAnalysis.filter(item => item.turnoverRate < 0.5).slice(0, 5)
    },
    alerts: lowStockAlerts,
    categoryPerformance,
    insights: generateInventoryInsights(stockLevels, turnoverAnalysis, lowStockAlerts)
  }
}

async function getCustomerInsights(storeId: string, startDate: Date) {
  const [customerMetrics, topCustomers, loyaltyAnalysis, acquisitionTrends] = await Promise.all([
    // Customer metrics
    Promise.all([
      prisma.customer.count({
        where: { storeId, isActive: true }
      }),
      
      prisma.customer.count({
        where: {
          storeId,
          createdAt: { gte: startDate }
        }
      }),
      
      prisma.customer.aggregate({
        where: { storeId, isActive: true },
        _avg: { totalPurchases: true }
      })
    ]).then(([total, newCustomers, avgLifetime]) => ({
      totalCustomers: total,
      newCustomers,
      averageLifetimeValue: avgLifetime._avg.totalPurchases || 0
    })),
    
    // Top customers by spending
    prisma.customer.findMany({
      where: {
        storeId,
        isActive: true,
        sales: {
          some: {
            createdAt: { gte: startDate },
            status: 'COMPLETED'
          }
        }
      },
      include: {
        sales: {
          where: {
            createdAt: { gte: startDate },
            status: 'COMPLETED'
          },
          select: { total: true }
        }
      },
      take: 5
    }).then(customers => 
      customers
        .map(customer => ({
          id: customer.id,
          name: customer.name,
          phone: customer.phone,
          periodSpending: customer.sales.reduce((sum, sale) => sum + sale.total, 0),
          orderCount: customer.sales.length
        }))
        .sort((a, b) => b.periodSpending - a.periodSpending)
    ),
    
    // Loyalty tier distribution
    prisma.customer.groupBy({
      by: ['loyaltyTier'],
      where: { storeId, isActive: true },
      _count: true,
      _avg: { totalPurchases: true }
    }),
    
    // Customer acquisition trends
    prisma.customer.findMany({
      where: {
        storeId,
        createdAt: { gte: startDate }
      },
      select: { createdAt: true }
    }).then(customers => {
      const dailyData = new Map()
      customers.forEach(customer => {
        const date = customer.createdAt.toISOString().split('T')[0]
        dailyData.set(date, (dailyData.get(date) || 0) + 1)
      })
      return Array.from(dailyData.entries()).map(([date, count]) => ({ date, count }))
    })
  ])

  return {
    metrics: customerMetrics,
    topCustomers,
    loyaltyDistribution: loyaltyAnalysis,
    acquisitionTrends,
    insights: generateCustomerInsights(customerMetrics, topCustomers, loyaltyAnalysis)
  }
}

async function getOperationalInsights(storeId: string, startDate: Date) {
  const [staffPerformance, systemHealth, recentActivity] = await Promise.all([
    // Staff performance
    prisma.sale.groupBy({
      by: ['createdById'],
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED'
      },
      _sum: { total: true },
      _count: true
    }).then(async (sales) => {
      const userIds = sales.map(sale => sale.createdById).filter(Boolean)
      const users = await prisma.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, name: true }
      })
      
      return sales.map(sale => ({
        user: users.find(u => u.id === sale.createdById),
        totalSales: sale._sum.total || 0,
        transactionCount: sale._count
      })).sort((a, b) => b.totalSales - a.totalSales)
    }),
    
    // System health indicators
    Promise.all([
      prisma.notification.count({
        where: {
          storeId,
          type: 'ERROR',
          createdAt: { gte: startDate }
        }
      }),
      
      prisma.auditLog.count({
        where: {
          storeId,
          createdAt: { gte: startDate }
        }
      })
    ]).then(([errors, activities]) => ({
      errorCount: errors,
      activityCount: activities,
      healthScore: Math.max(0, 100 - (errors * 5)) // Simple health score
    })),
    
    // Recent activity summary
    prisma.auditLog.findMany({
      where: {
        storeId,
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
      },
      include: {
        user: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })
  ])

  return {
    staffPerformance,
    systemHealth,
    recentActivity,
    insights: generateOperationalInsights(staffPerformance, systemHealth)
  }
}

async function getBusinessAlerts(storeId: string) {
  const [lowStockAlerts, overduePayments, systemAlerts] = await Promise.all([
    // Low stock alerts
    prisma.inventory.count({
      where: {
        storeId,
        quantity: { lte: prisma.inventory.fields.reorderLevel }
      }
    }),
    
    // Overdue payments
    prisma.sale.count({
      where: {
        storeId,
        paymentStatus: { in: ['PENDING', 'PARTIAL'] },
        createdAt: { lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // 7 days old
      }
    }),
    
    // System alerts
    prisma.notification.count({
      where: {
        storeId,
        type: { in: ['ERROR', 'WARNING'] },
        readAt: null
      }
    })
  ])

  return {
    lowStock: lowStockAlerts,
    overduePayments,
    systemAlerts,
    totalAlerts: lowStockAlerts + overduePayments + systemAlerts
  }
}

async function getBusinessForecasts(storeId: string, startDate: Date) {
  // Simple forecasting based on trends
  const salesData = await prisma.sale.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate },
      status: 'COMPLETED'
    },
    select: {
      createdAt: true,
      total: true
    },
    orderBy: { createdAt: 'asc' }
  })

  const dailyRevenue = new Map()
  salesData.forEach(sale => {
    const date = sale.createdAt.toISOString().split('T')[0]
    dailyRevenue.set(date, (dailyRevenue.get(date) || 0) + sale.total)
  })

  const revenueArray = Array.from(dailyRevenue.values())
  const avgDailyRevenue = revenueArray.reduce((sum, val) => sum + val, 0) / revenueArray.length

  return {
    nextWeekRevenue: avgDailyRevenue * 7,
    nextMonthRevenue: avgDailyRevenue * 30,
    confidence: revenueArray.length > 7 ? 0.7 : 0.4
  }
}

// Insight generation functions

function generateRevenueInsights(trends: any[], topProducts: any[], paymentMethods: any[]) {
  const insights = []
  
  if (trends.length > 1) {
    const recentTrend = trends.slice(-7).reduce((sum, day) => sum + day.revenue, 0)
    const previousTrend = trends.slice(-14, -7).reduce((sum, day) => sum + day.revenue, 0)
    
    if (recentTrend > previousTrend * 1.1) {
      insights.push({
        type: 'positive',
        message: 'Revenue is trending upward in the last week',
        impact: 'high'
      })
    } else if (recentTrend < previousTrend * 0.9) {
      insights.push({
        type: 'warning',
        message: 'Revenue has declined in the last week',
        impact: 'high'
      })
    }
  }
  
  if (topProducts.length > 0) {
    const topProduct = topProducts[0]
    insights.push({
      type: 'info',
      message: `${topProduct.product.name} is your top revenue generator`,
      impact: 'medium'
    })
  }
  
  return insights
}

function generateInventoryInsights(stockLevels: any, turnover: any[], lowStock: any[]) {
  const insights = []
  
  if (stockLevels.lowStock > 0) {
    insights.push({
      type: 'warning',
      message: `${stockLevels.lowStock} products are running low on stock`,
      impact: 'high'
    })
  }
  
  if (stockLevels.outOfStock > 0) {
    insights.push({
      type: 'critical',
      message: `${stockLevels.outOfStock} products are out of stock`,
      impact: 'critical'
    })
  }
  
  const slowMoving = turnover.filter(item => item.turnoverRate < 0.5).length
  if (slowMoving > 5) {
    insights.push({
      type: 'info',
      message: `${slowMoving} products have slow inventory turnover`,
      impact: 'medium'
    })
  }
  
  return insights
}

function generateCustomerInsights(metrics: any, topCustomers: any[], loyalty: any[]) {
  const insights = []
  
  if (metrics.newCustomers > 0) {
    insights.push({
      type: 'positive',
      message: `Acquired ${metrics.newCustomers} new customers this period`,
      impact: 'medium'
    })
  }
  
  const goldCustomers = loyalty.find(tier => tier.loyaltyTier === 'GOLD')?._count || 0
  if (goldCustomers > 0) {
    insights.push({
      type: 'positive',
      message: `${goldCustomers} customers have reached Gold loyalty tier`,
      impact: 'medium'
    })
  }
  
  return insights
}

function generateOperationalInsights(staff: any[], health: any) {
  const insights = []
  
  if (health.healthScore < 80) {
    insights.push({
      type: 'warning',
      message: 'System health score is below optimal',
      impact: 'medium'
    })
  }
  
  if (staff.length > 0) {
    const topPerformer = staff[0]
    insights.push({
      type: 'positive',
      message: `${topPerformer.user?.name || 'Top performer'} generated the highest sales`,
      impact: 'low'
    })
  }
  
  return insights
}

function calculateGrowthPercentage(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0
  return ((current - previous) / previous) * 100
}
