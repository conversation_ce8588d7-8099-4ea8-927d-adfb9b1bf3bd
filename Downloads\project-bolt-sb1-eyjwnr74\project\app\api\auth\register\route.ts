import { NextRequest, NextResponse } from 'next/server'
import { hashPassword, signToken, createAuthResponse } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const registerSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  password: z.string().min(6),
  phone: z.string().optional(),
  role: z.enum(['FOUNDER', 'SUPER_ADMIN', 'ADMIN', 'STAFF', 'DISTRIBUTOR']).optional(),
  storeId: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, password, phone, role = 'STAFF', storeId } = registerSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await hashPassword(password)

    // Create user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        phone,
        role,
        storeId
      },
      include: { store: true }
    })

    // Generate token
    const token = signToken({
      userId: user.id,
      email: user.email,
      role: user.role,
      storeId: user.storeId || undefined
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'REGISTER',
        module: 'USER',
        details: `New user ${user.email} registered with role ${user.role}`,
        userId: user.id,
        storeId: user.storeId || ''
      }
    })

    return NextResponse.json(createAuthResponse(user, token), { status: 201 })
  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}