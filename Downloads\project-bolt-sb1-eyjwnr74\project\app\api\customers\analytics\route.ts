import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/customers/analytics - Get comprehensive customer analytics
export const GET = withPermission('CUSTOMER', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const period = searchParams.get('period') || '30' // days
  const includeSegmentation = searchParams.get('segmentation') === 'true'
  const includeLoyalty = searchParams.get('loyalty') === 'true'

  const periodDays = parseInt(period)
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - periodDays)

  try {
    const [
      customerSummary,
      customerGrowth,
      customerSegmentation,
      loyaltyAnalytics,
      topCustomers,
      customerRetention,
      purchaseBehavior
    ] = await Promise.all([
      // Customer Summary
      getCustomerSummary(storeId, startDate),
      
      // Customer Growth
      getCustomerGrowth(storeId, startDate),
      
      // Customer Segmentation (if requested)
      includeSegmentation ? getCustomerSegmentation(storeId) : null,
      
      // Loyalty Analytics (if requested)
      includeLoyalty ? getLoyaltyAnalytics(storeId, startDate) : null,
      
      // Top Customers
      getTopCustomers(storeId, startDate, 10),
      
      // Customer Retention
      getCustomerRetention(storeId, startDate),
      
      // Purchase Behavior
      getPurchaseBehavior(storeId, startDate)
    ])

    const analytics = {
      period: {
        days: periodDays,
        startDate,
        endDate: new Date()
      },
      summary: customerSummary,
      growth: customerGrowth,
      topCustomers,
      retention: customerRetention,
      purchaseBehavior,
      ...(customerSegmentation && { segmentation: customerSegmentation }),
      ...(loyaltyAnalytics && { loyalty: loyaltyAnalytics })
    }

    return createSuccessResponse(analytics, 'Customer analytics retrieved successfully')

  } catch (error) {
    console.error('Error fetching customer analytics:', error)
    return createErrorResponse('Failed to fetch customer analytics', 500)
  }
})

// Helper functions for analytics

async function getCustomerSummary(storeId: string, startDate: Date) {
  const [totalCustomers, newCustomers, activeCustomers, avgLifetimeValue] = await Promise.all([
    // Total customers
    prisma.customer.count({
      where: { storeId, isActive: true }
    }),
    
    // New customers in period
    prisma.customer.count({
      where: {
        storeId,
        isActive: true,
        createdAt: { gte: startDate }
      }
    }),
    
    // Active customers (made purchase in period)
    prisma.customer.count({
      where: {
        storeId,
        isActive: true,
        sales: {
          some: {
            createdAt: { gte: startDate },
            status: 'COMPLETED'
          }
        }
      }
    }),
    
    // Average lifetime value
    prisma.customer.aggregate({
      where: { storeId, isActive: true },
      _avg: { totalPurchases: true }
    })
  ])

  return {
    totalCustomers,
    newCustomers,
    activeCustomers,
    avgLifetimeValue: avgLifetimeValue._avg.totalPurchases || 0,
    activeRate: totalCustomers > 0 ? (activeCustomers / totalCustomers) * 100 : 0
  }
}

async function getCustomerGrowth(storeId: string, startDate: Date) {
  const customers = await prisma.customer.findMany({
    where: {
      storeId,
      isActive: true,
      createdAt: { gte: startDate }
    },
    select: {
      createdAt: true
    },
    orderBy: { createdAt: 'asc' }
  })

  const growthMap = new Map()
  
  customers.forEach(customer => {
    const date = customer.createdAt.toISOString().split('T')[0]
    const existing = growthMap.get(date) || 0
    growthMap.set(date, existing + 1)
  })

  return Array.from(growthMap.entries()).map(([date, count]) => ({
    date,
    newCustomers: count
  })).sort((a, b) => a.date.localeCompare(b.date))
}

async function getCustomerSegmentation(storeId: string) {
  const customers = await prisma.customer.findMany({
    where: { storeId, isActive: true },
    select: {
      id: true,
      totalPurchases: true,
      loyaltyTier: true,
      createdAt: true,
      lastPurchaseDate: true,
      sales: {
        where: { status: 'COMPLETED' },
        select: {
          total: true,
          createdAt: true
        }
      }
    }
  })

  const segments = {
    byValue: { high: 0, medium: 0, low: 0 },
    byFrequency: { frequent: 0, occasional: 0, rare: 0 },
    byRecency: { recent: 0, lapsed: 0, inactive: 0 },
    byTier: { BRONZE: 0, SILVER: 0, GOLD: 0, PLATINUM: 0 }
  }

  const now = new Date()
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)

  customers.forEach(customer => {
    // Value segmentation
    if (customer.totalPurchases >= 50000) segments.byValue.high++
    else if (customer.totalPurchases >= 10000) segments.byValue.medium++
    else segments.byValue.low++

    // Frequency segmentation
    const purchaseCount = customer.sales.length
    if (purchaseCount >= 10) segments.byFrequency.frequent++
    else if (purchaseCount >= 3) segments.byFrequency.occasional++
    else segments.byFrequency.rare++

    // Recency segmentation
    const lastPurchase = customer.lastPurchaseDate
    if (lastPurchase && lastPurchase >= thirtyDaysAgo) segments.byRecency.recent++
    else if (lastPurchase && lastPurchase >= ninetyDaysAgo) segments.byRecency.lapsed++
    else segments.byRecency.inactive++

    // Tier segmentation
    segments.byTier[customer.loyaltyTier]++
  })

  return segments
}

async function getLoyaltyAnalytics(storeId: string, startDate: Date) {
  const [pointsEarned, pointsRedeemed, activeMembers, tierDistribution] = await Promise.all([
    // Points earned in period
    prisma.loyaltyTransaction.aggregate({
      where: {
        storeId,
        createdAt: { gte: startDate },
        type: { in: ['EARN', 'ADJUST'] },
        points: { gt: 0 }
      },
      _sum: { points: true }
    }),
    
    // Points redeemed in period
    prisma.loyaltyTransaction.aggregate({
      where: {
        storeId,
        createdAt: { gte: startDate },
        type: 'REDEEM'
      },
      _sum: { points: true }
    }),
    
    // Active loyalty members
    prisma.customer.count({
      where: {
        storeId,
        isActive: true,
        loyaltyPoints: { gt: 0 }
      }
    }),
    
    // Tier distribution
    prisma.customer.groupBy({
      by: ['loyaltyTier'],
      where: { storeId, isActive: true },
      _count: true
    })
  ])

  return {
    pointsEarned: pointsEarned._sum.points || 0,
    pointsRedeemed: Math.abs(pointsRedeemed._sum.points || 0),
    activeMembers,
    tierDistribution: tierDistribution.reduce((acc, tier) => {
      acc[tier.loyaltyTier] = tier._count
      return acc
    }, {} as Record<string, number>)
  }
}

async function getTopCustomers(storeId: string, startDate: Date, limit: number) {
  const topCustomers = await prisma.customer.findMany({
    where: {
      storeId,
      isActive: true,
      sales: {
        some: {
          createdAt: { gte: startDate },
          status: 'COMPLETED'
        }
      }
    },
    include: {
      sales: {
        where: {
          createdAt: { gte: startDate },
          status: 'COMPLETED'
        },
        select: {
          total: true,
          createdAt: true
        }
      }
    },
    take: limit * 2 // Get more to sort properly
  })

  return topCustomers
    .map(customer => {
      const periodSales = customer.sales.reduce((sum, sale) => sum + sale.total, 0)
      const purchaseCount = customer.sales.length
      const avgOrderValue = purchaseCount > 0 ? periodSales / purchaseCount : 0
      
      return {
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: customer.phone,
        loyaltyTier: customer.loyaltyTier,
        totalPurchases: customer.totalPurchases,
        periodSales,
        purchaseCount,
        avgOrderValue,
        lastPurchase: customer.lastPurchaseDate
      }
    })
    .sort((a, b) => b.periodSales - a.periodSales)
    .slice(0, limit)
}

async function getCustomerRetention(storeId: string, startDate: Date) {
  const customers = await prisma.customer.findMany({
    where: { storeId, isActive: true },
    include: {
      sales: {
        where: { status: 'COMPLETED' },
        select: {
          createdAt: true
        },
        orderBy: { createdAt: 'asc' }
      }
    }
  })

  let newCustomers = 0
  let returningCustomers = 0
  let churnedCustomers = 0

  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  const sixtyDaysAgo = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)

  customers.forEach(customer => {
    const firstPurchase = customer.sales[0]?.createdAt
    const lastPurchase = customer.sales[customer.sales.length - 1]?.createdAt

    if (firstPurchase && firstPurchase >= startDate) {
      newCustomers++
    }

    if (customer.sales.length > 1) {
      returningCustomers++
    }

    if (lastPurchase && lastPurchase < sixtyDaysAgo && customer.sales.length > 0) {
      churnedCustomers++
    }
  })

  const totalCustomers = customers.length
  const retentionRate = totalCustomers > 0 ? (returningCustomers / totalCustomers) * 100 : 0
  const churnRate = totalCustomers > 0 ? (churnedCustomers / totalCustomers) * 100 : 0

  return {
    newCustomers,
    returningCustomers,
    churnedCustomers,
    retentionRate,
    churnRate,
    totalCustomers
  }
}

async function getPurchaseBehavior(storeId: string, startDate: Date) {
  const sales = await prisma.sale.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate },
      status: 'COMPLETED',
      customerId: { not: null }
    },
    select: {
      total: true,
      createdAt: true,
      customerId: true
    }
  })

  const customerPurchases = new Map()
  
  sales.forEach(sale => {
    const customerId = sale.customerId!
    const existing = customerPurchases.get(customerId) || []
    existing.push(sale)
    customerPurchases.set(customerId, existing)
  })

  const purchaseFrequencies = Array.from(customerPurchases.values()).map(purchases => purchases.length)
  const orderValues = sales.map(sale => sale.total)

  return {
    avgOrderValue: orderValues.length > 0 ? orderValues.reduce((a, b) => a + b, 0) / orderValues.length : 0,
    avgPurchaseFrequency: purchaseFrequencies.length > 0 ? purchaseFrequencies.reduce((a, b) => a + b, 0) / purchaseFrequencies.length : 0,
    totalOrders: sales.length,
    uniqueCustomers: customerPurchases.size,
    repeatCustomerRate: customerPurchases.size > 0 ? (Array.from(customerPurchases.values()).filter(purchases => purchases.length > 1).length / customerPurchases.size) * 100 : 0
  }
}
