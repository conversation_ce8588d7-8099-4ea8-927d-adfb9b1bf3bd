import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const ticketSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  category: z.enum(['TECHNICAL', 'BILLING', 'FEATURE_REQUEST', 'BUG_REPORT', 'GENERAL', 'ACCOUNT']),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  type: z.enum(['QUESTION', 'ISSUE', 'REQUEST', 'COMPLAINT']).default('QUESTION'),
  attachments: z.array(z.object({
    fileName: z.string(),
    fileUrl: z.string(),
    fileSize: z.number(),
    mimeType: z.string()
  })).optional(),
  metadata: z.record(z.any()).optional(),
  customerEmail: z.string().email().optional(),
  customerPhone: z.string().optional()
})

const responseSchema = z.object({
  message: z.string().min(1, 'Message is required'),
  isInternal: z.boolean().default(false),
  attachments: z.array(z.object({
    fileName: z.string(),
    fileUrl: z.string(),
    fileSize: z.number(),
    mimeType: z.string()
  })).optional(),
  suggestedActions: z.array(z.string()).optional()
})

// GET /api/support/tickets - Get support tickets with filtering
export const GET = withPermission('SUPPORT', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const status = searchParams.get('status')
  const category = searchParams.get('category')
  const priority = searchParams.get('priority')
  const assignedTo = searchParams.get('assignedTo')
  const createdBy = searchParams.get('createdBy')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const includeResponses = searchParams.get('responses') === 'true'

  try {
    // Build filters
    const where: any = {
      storeId,
      ...buildSearchFilter(search, ['title', 'description', 'ticketNumber']),
      ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
    }

    if (status) {
      where.status = status
    }

    if (category) {
      where.category = category
    }

    if (priority) {
      where.priority = priority
    }

    if (assignedTo) {
      where.assignedToId = assignedTo
    }

    if (createdBy) {
      where.createdById = createdBy
    }

    // Non-admin users can only see their own tickets
    if (!['FOUNDER', 'SUPER_ADMIN', 'ADMIN'].includes(user.role)) {
      where.createdById = user.id
    }

    // Get total count
    const total = await prisma.supportTicket.count({ where })

    // Get tickets with pagination
    const tickets = await prisma.supportTicket.findMany({
      where,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        responses: includeResponses ? {
          include: {
            createdBy: {
              select: {
                id: true,
                name: true,
                role: true
              }
            }
          },
          orderBy: { createdAt: 'asc' }
        } : false,
        _count: {
          select: {
            responses: true
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    })

    // Add calculated fields
    const ticketsWithCalculations = tickets.map(ticket => ({
      ...ticket,
      responseCount: ticket._count.responses,
      timeToFirstResponse: calculateTimeToFirstResponse(ticket),
      timeSinceLastUpdate: getTimeSinceLastUpdate(ticket),
      isOverdue: isTicketOverdue(ticket),
      escalationLevel: calculateEscalationLevel(ticket),
      customerSatisfaction: ticket.customerRating ? {
        rating: ticket.customerRating,
        feedback: ticket.customerFeedback
      } : null
    }))

    return createPaginatedResponse(ticketsWithCalculations, page, limit, total)

  } catch (error) {
    console.error('Error fetching support tickets:', error)
    return createErrorResponse('Failed to fetch support tickets', 500)
  }
})

// POST /api/support/tickets - Create new support ticket
export const POST = withPermission('SUPPORT', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = ticketSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Generate ticket number
      const ticketCount = await tx.supportTicket.count({ where: { storeId } })
      const ticketNumber = `TKT-${String(ticketCount + 1).padStart(6, '0')}`

      // Auto-assign based on category and workload
      const assignedToId = await autoAssignTicket(data.category, storeId, tx)

      // Create ticket
      const ticket = await tx.supportTicket.create({
        data: {
          ticketNumber,
          title: data.title,
          description: data.description,
          category: data.category,
          priority: data.priority,
          type: data.type,
          status: 'OPEN',
          attachments: data.attachments,
          metadata: data.metadata,
          customerEmail: data.customerEmail,
          customerPhone: data.customerPhone,
          assignedToId,
          createdById: user.id,
          storeId
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      // Create initial response if this is a system-generated ticket
      if (data.metadata?.autoGenerated) {
        await tx.supportTicketResponse.create({
          data: {
            ticketId: ticket.id,
            message: 'This ticket was automatically generated by the system.',
            isInternal: true,
            createdById: user.id,
            storeId
          }
        })
      }

      return ticket
    })

    // Send notifications
    await sendTicketNotifications(result, 'CREATED')

    // Create audit log
    await createAuditLog(
      'CREATE',
      'SUPPORT_TICKET',
      `Created support ticket: ${result.ticketNumber} - ${result.title}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Support ticket created successfully')

  } catch (error) {
    console.error('Error creating support ticket:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create support ticket',
      400
    )
  }
})

// PUT /api/support/tickets/[id] - Update support ticket
export const PUT = withPermission('SUPPORT', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const ticketId = url.pathname.split('/').pop()
  const body = await request.json()

  const updateSchema = z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    category: z.enum(['TECHNICAL', 'BILLING', 'FEATURE_REQUEST', 'BUG_REPORT', 'GENERAL', 'ACCOUNT']).optional(),
    priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
    status: z.enum(['OPEN', 'IN_PROGRESS', 'WAITING_CUSTOMER', 'RESOLVED', 'CLOSED']).optional(),
    assignedToId: z.string().optional(),
    resolution: z.string().optional(),
    customerRating: z.number().min(1).max(5).optional(),
    customerFeedback: z.string().optional(),
    metadata: z.record(z.any()).optional()
  })

  const data = updateSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Get current ticket
      const ticket = await tx.supportTicket.findFirst({
        where: {
          id: ticketId,
          storeId
        }
      })

      if (!ticket) {
        throw new Error('Support ticket not found')
      }

      // Check permissions
      if (!['FOUNDER', 'SUPER_ADMIN', 'ADMIN'].includes(user.role) && 
          ticket.createdById !== user.id && ticket.assignedToId !== user.id) {
        throw new Error('Insufficient permissions to update this ticket')
      }

      // Validate status transitions
      if (data.status && !isValidStatusTransition(ticket.status, data.status)) {
        throw new Error(`Invalid status transition from ${ticket.status} to ${data.status}`)
      }

      // Update ticket
      const updatedTicket = await tx.supportTicket.update({
        where: { id: ticketId },
        data: {
          ...data,
          resolvedAt: data.status === 'RESOLVED' ? new Date() : undefined,
          closedAt: data.status === 'CLOSED' ? new Date() : undefined,
          updatedAt: new Date()
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      // Create activity log for significant changes
      const changes = []
      if (data.status && data.status !== ticket.status) {
        changes.push(`Status changed from ${ticket.status} to ${data.status}`)
      }
      if (data.priority && data.priority !== ticket.priority) {
        changes.push(`Priority changed from ${ticket.priority} to ${data.priority}`)
      }
      if (data.assignedToId && data.assignedToId !== ticket.assignedToId) {
        changes.push(`Ticket reassigned`)
      }

      if (changes.length > 0) {
        await tx.supportTicketResponse.create({
          data: {
            ticketId,
            message: `Ticket updated: ${changes.join(', ')}`,
            isInternal: true,
            createdById: user.id,
            storeId
          }
        })
      }

      return updatedTicket
    })

    // Send notifications for status changes
    if (data.status) {
      await sendTicketNotifications(result, 'UPDATED')
    }

    // Create audit log
    await createAuditLog(
      'UPDATE',
      'SUPPORT_TICKET',
      `Updated support ticket: ${result.ticketNumber}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Support ticket updated successfully')

  } catch (error) {
    console.error('Error updating support ticket:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to update support ticket',
      400
    )
  }
})

// POST /api/support/tickets/[id]/responses - Add response to ticket
export const POST = withPermission('SUPPORT', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathParts = url.pathname.split('/')
  const ticketId = pathParts[pathParts.length - 2] // Get ticket ID from path
  const body = await request.json()
  const data = responseSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Verify ticket exists and user has access
      const ticket = await tx.supportTicket.findFirst({
        where: {
          id: ticketId,
          storeId
        }
      })

      if (!ticket) {
        throw new Error('Support ticket not found')
      }

      // Check permissions
      if (!['FOUNDER', 'SUPER_ADMIN', 'ADMIN'].includes(user.role) && 
          ticket.createdById !== user.id && ticket.assignedToId !== user.id) {
        throw new Error('Insufficient permissions to respond to this ticket')
      }

      // Create response
      const response = await tx.supportTicketResponse.create({
        data: {
          ticketId,
          message: data.message,
          isInternal: data.isInternal,
          attachments: data.attachments,
          suggestedActions: data.suggestedActions,
          createdById: user.id,
          storeId
        },
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              role: true
            }
          }
        }
      })

      // Update ticket status if needed
      let newStatus = ticket.status
      if (ticket.status === 'OPEN' && ['ADMIN', 'SUPER_ADMIN', 'FOUNDER'].includes(user.role)) {
        newStatus = 'IN_PROGRESS'
      } else if (ticket.status === 'WAITING_CUSTOMER' && ticket.createdById === user.id) {
        newStatus = 'IN_PROGRESS'
      }

      if (newStatus !== ticket.status) {
        await tx.supportTicket.update({
          where: { id: ticketId },
          data: { 
            status: newStatus,
            updatedAt: new Date()
          }
        })
      }

      return { response, ticket: { ...ticket, status: newStatus } }
    })

    // Send notifications
    if (!data.isInternal) {
      await sendResponseNotifications(result.response, result.ticket)
    }

    return createSuccessResponse(result.response, 'Response added successfully')

  } catch (error) {
    console.error('Error adding ticket response:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to add response',
      400
    )
  }
})

// Helper functions

async function autoAssignTicket(category: string, storeId: string, tx: any): Promise<string | null> {
  // Get available support agents
  const agents = await tx.user.findMany({
    where: {
      storeId,
      role: { in: ['ADMIN', 'SUPER_ADMIN'] },
      isActive: true
    },
    include: {
      _count: {
        select: {
          assignedTickets: {
            where: {
              status: { in: ['OPEN', 'IN_PROGRESS'] }
            }
          }
        }
      }
    }
  })

  if (agents.length === 0) return null

  // Find agent with least workload
  const leastBusyAgent = agents.reduce((min, agent) => 
    agent._count.assignedTickets < min._count.assignedTickets ? agent : min
  )

  return leastBusyAgent.id
}

function calculateTimeToFirstResponse(ticket: any): number | null {
  if (!ticket.responses || ticket.responses.length === 0) return null
  
  const firstResponse = ticket.responses.find((r: any) => !r.isInternal)
  if (!firstResponse) return null
  
  const createdAt = new Date(ticket.createdAt)
  const responseAt = new Date(firstResponse.createdAt)
  
  return Math.floor((responseAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60)) // hours
}

function getTimeSinceLastUpdate(ticket: any): number {
  const lastUpdate = new Date(ticket.updatedAt)
  const now = new Date()
  
  return Math.floor((now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60)) // hours
}

function isTicketOverdue(ticket: any): boolean {
  if (ticket.status === 'CLOSED' || ticket.status === 'RESOLVED') return false
  
  const slaHours = getSLAHours(ticket.priority)
  const timeSinceCreation = (Date.now() - new Date(ticket.createdAt).getTime()) / (1000 * 60 * 60)
  
  return timeSinceCreation > slaHours
}

function getSLAHours(priority: string): number {
  switch (priority) {
    case 'URGENT': return 2
    case 'HIGH': return 8
    case 'MEDIUM': return 24
    case 'LOW': return 72
    default: return 24
  }
}

function calculateEscalationLevel(ticket: any): number {
  const timeSinceCreation = (Date.now() - new Date(ticket.createdAt).getTime()) / (1000 * 60 * 60)
  const slaHours = getSLAHours(ticket.priority)
  
  if (timeSinceCreation > slaHours * 2) return 3 // Critical escalation
  if (timeSinceCreation > slaHours * 1.5) return 2 // High escalation
  if (timeSinceCreation > slaHours) return 1 // Low escalation
  return 0 // No escalation
}

function isValidStatusTransition(currentStatus: string, newStatus: string): boolean {
  const validTransitions: Record<string, string[]> = {
    'OPEN': ['IN_PROGRESS', 'WAITING_CUSTOMER', 'RESOLVED', 'CLOSED'],
    'IN_PROGRESS': ['WAITING_CUSTOMER', 'RESOLVED', 'CLOSED', 'OPEN'],
    'WAITING_CUSTOMER': ['IN_PROGRESS', 'RESOLVED', 'CLOSED'],
    'RESOLVED': ['CLOSED', 'IN_PROGRESS'],
    'CLOSED': ['IN_PROGRESS'] // Can reopen if needed
  }
  
  return validTransitions[currentStatus]?.includes(newStatus) || false
}

async function sendTicketNotifications(ticket: any, action: string) {
  // Send notification to assigned agent
  if (ticket.assignedToId) {
    await prisma.notification.create({
      data: {
        title: `Support Ticket ${action}`,
        message: `Ticket ${ticket.ticketNumber}: ${ticket.title}`,
        type: 'INFO',
        category: 'SUPPORT',
        userId: ticket.assignedToId,
        storeId: ticket.storeId
      }
    })
  }

  // Send notification to ticket creator if not the same as current user
  if (ticket.createdById !== ticket.assignedToId) {
    await prisma.notification.create({
      data: {
        title: `Your Support Ticket ${action}`,
        message: `Ticket ${ticket.ticketNumber}: ${ticket.title}`,
        type: 'INFO',
        category: 'SUPPORT',
        userId: ticket.createdById,
        storeId: ticket.storeId
      }
    })
  }
}

async function sendResponseNotifications(response: any, ticket: any) {
  // Notify ticket creator about new response
  if (response.createdById !== ticket.createdById) {
    await prisma.notification.create({
      data: {
        title: 'New Response on Your Ticket',
        message: `New response on ticket ${ticket.ticketNumber}`,
        type: 'INFO',
        category: 'SUPPORT',
        userId: ticket.createdById,
        storeId: ticket.storeId
      }
    })
  }

  // Notify assigned agent about customer response
  if (ticket.assignedToId && response.createdById !== ticket.assignedToId) {
    await prisma.notification.create({
      data: {
        title: 'New Customer Response',
        message: `Customer responded to ticket ${ticket.ticketNumber}`,
        type: 'INFO',
        category: 'SUPPORT',
        userId: ticket.assignedToId,
        storeId: ticket.storeId
      }
    })
  }
}
