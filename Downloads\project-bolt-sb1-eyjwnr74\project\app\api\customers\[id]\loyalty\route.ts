import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/customers/[id]/loyalty - Get customer loyalty information
export const GET = withPermission('CUSTOMER', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const customerId = pathSegments[pathSegments.length - 2] // Get customer ID from path

  // Check if customer exists and belongs to store
  const customer = await prisma.customer.findFirst({
    where: {
      id: customerId,
      storeId,
      isActive: true
    },
    include: {
      loyaltyTransactions: {
        orderBy: { createdAt: 'desc' },
        take: 20,
        include: {
          sale: {
            select: {
              saleNo: true,
              total: true
            }
          }
        }
      }
    }
  })

  if (!customer) {
    return createErrorResponse('Customer not found', 404)
  }

  // Calculate loyalty metrics
  const loyaltyMetrics = await calculateLoyaltyMetrics(customerId, storeId)

  const loyaltyInfo = {
    customer: {
      id: customer.id,
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      loyaltyTier: customer.loyaltyTier,
      loyaltyPoints: customer.loyaltyPoints,
      totalPurchases: customer.totalPurchases,
      joinDate: customer.createdAt
    },
    metrics: loyaltyMetrics,
    recentTransactions: customer.loyaltyTransactions,
    tierBenefits: getTierBenefits(customer.loyaltyTier),
    nextTierRequirement: getNextTierRequirement(customer.loyaltyTier, customer.totalPurchases)
  }

  return createSuccessResponse(loyaltyInfo)
})

// POST /api/customers/[id]/loyalty - Add/Redeem loyalty points
export const POST = withPermission('CUSTOMER', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const customerId = pathSegments[pathSegments.length - 2] // Get customer ID from path
  const body = await request.json()

  const loyaltySchema = z.object({
    type: z.enum(['EARN', 'REDEEM', 'ADJUST']),
    points: z.number().min(1, 'Points must be positive'),
    reason: z.string().min(1, 'Reason is required'),
    saleId: z.string().optional(),
    notes: z.string().optional()
  })

  const { type, points, reason, saleId, notes } = loyaltySchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if customer exists and belongs to store
      const customer = await tx.customer.findFirst({
        where: {
          id: customerId,
          storeId,
          isActive: true
        }
      })

      if (!customer) {
        throw new Error('Customer not found')
      }

      // Validate redemption
      if (type === 'REDEEM' && customer.loyaltyPoints < points) {
        throw new Error(`Insufficient loyalty points. Available: ${customer.loyaltyPoints}, Required: ${points}`)
      }

      // Calculate new points balance
      let newPoints = customer.loyaltyPoints
      switch (type) {
        case 'EARN':
        case 'ADJUST':
          newPoints += points
          break
        case 'REDEEM':
          newPoints -= points
          break
      }

      // Update customer points
      const updatedCustomer = await tx.customer.update({
        where: { id: customerId },
        data: {
          loyaltyPoints: Math.max(0, newPoints)
        }
      })

      // Create loyalty transaction record
      const loyaltyTransaction = await tx.loyaltyTransaction.create({
        data: {
          customerId,
          type,
          points: type === 'REDEEM' ? -points : points,
          reason,
          saleId,
          notes,
          balanceAfter: updatedCustomer.loyaltyPoints,
          storeId,
          createdById: user.id
        },
        include: {
          sale: {
            select: {
              saleNo: true,
              total: true
            }
          },
          createdBy: {
            select: {
              name: true
            }
          }
        }
      })

      // Check for tier upgrade
      const newTier = calculateTier(updatedCustomer.totalPurchases)
      if (newTier !== customer.loyaltyTier) {
        await tx.customer.update({
          where: { id: customerId },
          data: { loyaltyTier: newTier }
        })

        // Create notification for tier upgrade
        await tx.notification.create({
          data: {
            title: `Loyalty Tier Upgraded!`,
            message: `Congratulations! You've been upgraded to ${newTier} tier.`,
            type: 'SUCCESS',
            customerId,
            storeId
          }
        })
      }

      return {
        customer: updatedCustomer,
        transaction: loyaltyTransaction,
        tierUpgrade: newTier !== customer.loyaltyTier ? newTier : null
      }
    })

    // Create audit log
    await createAuditLog(
      type,
      'CUSTOMER',
      `Loyalty ${type.toLowerCase()}: ${points} points for ${result.customer.name} - Reason: ${reason}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, `Loyalty points ${type.toLowerCase()}ed successfully`)

  } catch (error) {
    console.error('Error processing loyalty transaction:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to process loyalty transaction',
      400
    )
  }
})

// Helper functions

async function calculateLoyaltyMetrics(customerId: string, storeId: string) {
  const [
    totalEarned,
    totalRedeemed,
    transactionCount,
    lastTransaction,
    monthlyActivity
  ] = await Promise.all([
    // Total points earned
    prisma.loyaltyTransaction.aggregate({
      where: {
        customerId,
        storeId,
        type: { in: ['EARN', 'ADJUST'] },
        points: { gt: 0 }
      },
      _sum: { points: true }
    }),

    // Total points redeemed
    prisma.loyaltyTransaction.aggregate({
      where: {
        customerId,
        storeId,
        type: 'REDEEM'
      },
      _sum: { points: true }
    }),

    // Transaction count
    prisma.loyaltyTransaction.count({
      where: { customerId, storeId }
    }),

    // Last transaction
    prisma.loyaltyTransaction.findFirst({
      where: { customerId, storeId },
      orderBy: { createdAt: 'desc' }
    }),

    // Monthly activity (last 12 months)
    prisma.loyaltyTransaction.findMany({
      where: {
        customerId,
        storeId,
        createdAt: {
          gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) // Last year
        }
      },
      select: {
        createdAt: true,
        points: true,
        type: true
      }
    })
  ])

  // Group monthly activity
  const monthlyData = new Map()
  monthlyActivity.forEach(transaction => {
    const month = transaction.createdAt.toISOString().substring(0, 7) // YYYY-MM
    const existing = monthlyData.get(month) || { earned: 0, redeemed: 0 }
    
    if (transaction.type === 'REDEEM') {
      existing.redeemed += Math.abs(transaction.points)
    } else {
      existing.earned += transaction.points
    }
    
    monthlyData.set(month, existing)
  })

  return {
    totalEarned: totalEarned._sum.points || 0,
    totalRedeemed: Math.abs(totalRedeemed._sum.points || 0),
    netPoints: (totalEarned._sum.points || 0) - Math.abs(totalRedeemed._sum.points || 0),
    transactionCount,
    lastActivity: lastTransaction?.createdAt,
    monthlyActivity: Array.from(monthlyData.entries()).map(([month, data]) => ({
      month,
      ...data
    })).sort((a, b) => a.month.localeCompare(b.month))
  }
}

function getTierBenefits(tier: string) {
  const benefits = {
    BRONZE: {
      pointsMultiplier: 1,
      discountPercentage: 0,
      specialOffers: false,
      prioritySupport: false,
      freeDelivery: false
    },
    SILVER: {
      pointsMultiplier: 1.2,
      discountPercentage: 2,
      specialOffers: true,
      prioritySupport: false,
      freeDelivery: false
    },
    GOLD: {
      pointsMultiplier: 1.5,
      discountPercentage: 5,
      specialOffers: true,
      prioritySupport: true,
      freeDelivery: true
    },
    PLATINUM: {
      pointsMultiplier: 2,
      discountPercentage: 10,
      specialOffers: true,
      prioritySupport: true,
      freeDelivery: true
    }
  }

  return benefits[tier] || benefits.BRONZE
}

function getNextTierRequirement(currentTier: string, totalPurchases: number) {
  const tierThresholds = {
    BRONZE: { next: 'SILVER', required: 10000 },
    SILVER: { next: 'GOLD', required: 50000 },
    GOLD: { next: 'PLATINUM', required: 100000 },
    PLATINUM: { next: null, required: null }
  }

  const current = tierThresholds[currentTier]
  if (!current || !current.next) {
    return null
  }

  return {
    nextTier: current.next,
    requiredAmount: current.required,
    currentAmount: totalPurchases,
    remainingAmount: Math.max(0, current.required - totalPurchases),
    progress: Math.min(100, (totalPurchases / current.required) * 100)
  }
}

function calculateTier(totalPurchases: number): string {
  if (totalPurchases >= 100000) return 'PLATINUM'
  if (totalPurchases >= 50000) return 'GOLD'
  if (totalPurchases >= 10000) return 'SILVER'
  return 'BRONZE'
}
