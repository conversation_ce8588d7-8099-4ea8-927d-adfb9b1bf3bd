import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/suppliers/[id]/performance - Get supplier performance analytics
export const GET = withPermission('SUPPLIER', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const supplierId = pathSegments[pathSegments.length - 2] // Get supplier ID from path
  const { searchParams } = url
  const period = searchParams.get('period') || '90' // days
  const includeComparison = searchParams.get('comparison') === 'true'

  const periodDays = parseInt(period)
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - periodDays)

  // Check if supplier exists and belongs to store
  const supplier = await prisma.supplier.findFirst({
    where: {
      id: supplierId,
      storeId,
      isActive: true
    }
  })

  if (!supplier) {
    return createErrorResponse('Supplier not found', 404)
  }

  try {
    const [
      performanceSummary,
      deliveryPerformance,
      qualityMetrics,
      financialMetrics,
      orderHistory,
      comparisonData
    ] = await Promise.all([
      // Performance Summary
      getPerformanceSummary(supplierId, storeId, startDate),
      
      // Delivery Performance
      getDeliveryPerformance(supplierId, storeId, startDate),
      
      // Quality Metrics
      getQualityMetrics(supplierId, storeId, startDate),
      
      // Financial Metrics
      getFinancialMetrics(supplierId, storeId, startDate),
      
      // Order History
      getOrderHistory(supplierId, storeId, startDate),
      
      // Comparison with other suppliers (if requested)
      includeComparison ? getSupplierComparison(supplierId, storeId, startDate) : null
    ])

    const performance = {
      supplier: {
        id: supplier.id,
        name: supplier.name,
        phone: supplier.phone,
        email: supplier.email
      },
      period: {
        days: periodDays,
        startDate,
        endDate: new Date()
      },
      summary: performanceSummary,
      delivery: deliveryPerformance,
      quality: qualityMetrics,
      financial: financialMetrics,
      orderHistory,
      ...(comparisonData && { comparison: comparisonData })
    }

    return createSuccessResponse(performance, 'Supplier performance retrieved successfully')

  } catch (error) {
    console.error('Error fetching supplier performance:', error)
    return createErrorResponse('Failed to fetch supplier performance', 500)
  }
})

// Helper functions for performance analytics

async function getPerformanceSummary(supplierId: string, storeId: string, startDate: Date) {
  const [totalOrders, totalValue, avgOrderValue, onTimeDeliveries] = await Promise.all([
    // Total orders
    prisma.purchase.count({
      where: {
        supplierId,
        storeId,
        createdAt: { gte: startDate }
      }
    }),
    
    // Total value
    prisma.purchase.aggregate({
      where: {
        supplierId,
        storeId,
        createdAt: { gte: startDate },
        status: { in: ['COMPLETED', 'RECEIVED'] }
      },
      _sum: { totalAmount: true }
    }),
    
    // Average order value
    prisma.purchase.aggregate({
      where: {
        supplierId,
        storeId,
        createdAt: { gte: startDate },
        status: { in: ['COMPLETED', 'RECEIVED'] }
      },
      _avg: { totalAmount: true }
    }),
    
    // On-time deliveries (assuming deliveries within expected date)
    prisma.purchase.count({
      where: {
        supplierId,
        storeId,
        createdAt: { gte: startDate },
        status: 'RECEIVED',
        receivedAt: { lte: prisma.purchase.fields.expectedDeliveryDate }
      }
    })
  ])

  const totalReceived = await prisma.purchase.count({
    where: {
      supplierId,
      storeId,
      createdAt: { gte: startDate },
      status: 'RECEIVED'
    }
  })

  return {
    totalOrders,
    totalValue: totalValue._sum.totalAmount || 0,
    avgOrderValue: avgOrderValue._avg.totalAmount || 0,
    onTimeDeliveryRate: totalReceived > 0 ? (onTimeDeliveries / totalReceived) * 100 : 0,
    totalReceived
  }
}

async function getDeliveryPerformance(supplierId: string, storeId: string, startDate: Date) {
  const purchases = await prisma.purchase.findMany({
    where: {
      supplierId,
      storeId,
      createdAt: { gte: startDate },
      status: 'RECEIVED',
      expectedDeliveryDate: { not: null },
      receivedAt: { not: null }
    },
    select: {
      expectedDeliveryDate: true,
      receivedAt: true,
      createdAt: true
    }
  })

  let totalDelayDays = 0
  let onTimeCount = 0
  let earlyCount = 0
  let lateCount = 0

  purchases.forEach(purchase => {
    if (!purchase.expectedDeliveryDate || !purchase.receivedAt) return

    const expectedDate = new Date(purchase.expectedDeliveryDate)
    const receivedDate = new Date(purchase.receivedAt)
    const diffDays = Math.ceil((receivedDate.getTime() - expectedDate.getTime()) / (1000 * 60 * 60 * 24))

    if (diffDays === 0) onTimeCount++
    else if (diffDays < 0) earlyCount++
    else {
      lateCount++
      totalDelayDays += diffDays
    }
  })

  const totalDeliveries = purchases.length
  const avgDeliveryDelay = lateCount > 0 ? totalDelayDays / lateCount : 0

  return {
    totalDeliveries,
    onTimeDeliveries: onTimeCount,
    earlyDeliveries: earlyCount,
    lateDeliveries: lateCount,
    onTimeRate: totalDeliveries > 0 ? (onTimeCount / totalDeliveries) * 100 : 0,
    avgDeliveryDelay
  }
}

async function getQualityMetrics(supplierId: string, storeId: string, startDate: Date) {
  const [totalReturns, returnValue, defectiveItems] = await Promise.all([
    // Total returns
    prisma.purchaseReturn.count({
      where: {
        purchase: {
          supplierId,
          storeId
        },
        createdAt: { gte: startDate }
      }
    }),
    
    // Return value
    prisma.purchaseReturn.aggregate({
      where: {
        purchase: {
          supplierId,
          storeId
        },
        createdAt: { gte: startDate }
      },
      _sum: { totalAmount: true }
    }),
    
    // Defective items (returns due to quality issues)
    prisma.purchaseReturn.count({
      where: {
        purchase: {
          supplierId,
          storeId
        },
        createdAt: { gte: startDate },
        reason: { in: ['DAMAGED', 'QUALITY_ISSUE', 'EXPIRED'] }
      }
    })
  ])

  const totalPurchaseValue = await prisma.purchase.aggregate({
    where: {
      supplierId,
      storeId,
      createdAt: { gte: startDate },
      status: { in: ['COMPLETED', 'RECEIVED'] }
    },
    _sum: { totalAmount: true }
  })

  const purchaseValue = totalPurchaseValue._sum.totalAmount || 0
  const returnRate = purchaseValue > 0 ? ((returnValue._sum.totalAmount || 0) / purchaseValue) * 100 : 0

  return {
    totalReturns,
    returnValue: returnValue._sum.totalAmount || 0,
    defectiveItems,
    returnRate,
    qualityScore: Math.max(0, 100 - returnRate) // Simple quality score based on return rate
  }
}

async function getFinancialMetrics(supplierId: string, storeId: string, startDate: Date) {
  const [totalPaid, totalPending, avgPaymentDays] = await Promise.all([
    // Total paid amount
    prisma.purchase.aggregate({
      where: {
        supplierId,
        storeId,
        createdAt: { gte: startDate }
      },
      _sum: { paidAmount: true }
    }),
    
    // Total pending amount
    prisma.purchase.aggregate({
      where: {
        supplierId,
        storeId,
        createdAt: { gte: startDate }
      },
      _sum: { totalAmount: true }
    }).then(async (total) => {
      const paid = await prisma.purchase.aggregate({
        where: {
          supplierId,
          storeId,
          createdAt: { gte: startDate }
        },
        _sum: { paidAmount: true }
      })
      return (total._sum.totalAmount || 0) - (paid._sum.paidAmount || 0)
    }),
    
    // Average payment days (placeholder - would need payment date tracking)
    0 // This would require payment history tracking
  ])

  return {
    totalPaid: totalPaid._sum.paidAmount || 0,
    totalPending,
    avgPaymentDays,
    paymentReliability: 85 // Placeholder score
  }
}

async function getOrderHistory(supplierId: string, storeId: string, startDate: Date) {
  const orders = await prisma.purchase.findMany({
    where: {
      supplierId,
      storeId,
      createdAt: { gte: startDate }
    },
    select: {
      id: true,
      purchaseNo: true,
      purchaseDate: true,
      expectedDeliveryDate: true,
      totalAmount: true,
      status: true,
      receivedAt: true
    },
    orderBy: { createdAt: 'desc' },
    take: 20
  })

  return orders.map(order => ({
    ...order,
    isOnTime: order.receivedAt && order.expectedDeliveryDate 
      ? order.receivedAt <= order.expectedDeliveryDate 
      : null
  }))
}

async function getSupplierComparison(supplierId: string, storeId: string, startDate: Date) {
  // Get performance metrics for all suppliers
  const allSuppliers = await prisma.supplier.findMany({
    where: { storeId, isActive: true },
    select: { id: true, name: true }
  })

  const comparisons = await Promise.all(
    allSuppliers.map(async (supplier) => {
      const summary = await getPerformanceSummary(supplier.id, storeId, startDate)
      const delivery = await getDeliveryPerformance(supplier.id, storeId, startDate)
      const quality = await getQualityMetrics(supplier.id, storeId, startDate)
      
      return {
        supplierId: supplier.id,
        supplierName: supplier.name,
        totalOrders: summary.totalOrders,
        totalValue: summary.totalValue,
        onTimeRate: delivery.onTimeRate,
        qualityScore: quality.qualityScore,
        isCurrentSupplier: supplier.id === supplierId
      }
    })
  )

  // Calculate rankings
  const sortedByValue = [...comparisons].sort((a, b) => b.totalValue - a.totalValue)
  const sortedByOnTime = [...comparisons].sort((a, b) => b.onTimeRate - a.onTimeRate)
  const sortedByQuality = [...comparisons].sort((a, b) => b.qualityScore - a.qualityScore)

  const currentSupplier = comparisons.find(s => s.isCurrentSupplier)
  
  return {
    rankings: {
      byValue: sortedByValue.findIndex(s => s.supplierId === supplierId) + 1,
      byOnTimeDelivery: sortedByOnTime.findIndex(s => s.supplierId === supplierId) + 1,
      byQuality: sortedByQuality.findIndex(s => s.supplierId === supplierId) + 1
    },
    totalSuppliers: allSuppliers.length,
    topPerformers: {
      byValue: sortedByValue.slice(0, 3),
      byOnTimeDelivery: sortedByOnTime.slice(0, 3),
      byQuality: sortedByQuality.slice(0, 3)
    }
  }
}
