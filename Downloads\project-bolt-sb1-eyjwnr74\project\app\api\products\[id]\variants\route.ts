import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/products/[id]/variants - Get product variants
export const GET = withPermission('PRODUCT', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const productId = pathSegments[pathSegments.length - 2] // Get product ID from path

  // Check if product exists and belongs to store
  const product = await prisma.product.findFirst({
    where: {
      id: productId,
      storeId,
      isActive: true
    }
  })

  if (!product) {
    return createErrorResponse('Product not found', 404)
  }

  const variants = await prisma.productVariant.findMany({
    where: {
      productId,
      isActive: true
    },
    include: {
      inventory: {
        select: {
          quantity: true,
          reorderLevel: true
        }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  return createSuccessResponse(variants)
})

// POST /api/products/[id]/variants - Create product variant
export const POST = withPermission('PRODUCT', 'CREATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const productId = pathSegments[pathSegments.length - 2] // Get product ID from path
  const body = await request.json()

  const variantSchema = z.object({
    name: z.string().min(1, 'Variant name is required'),
    sku: z.string().min(1, 'SKU is required'),
    barcode: z.string().optional(),
    costPrice: z.number().min(0, 'Cost price must be positive'),
    sellingPrice: z.number().min(0, 'Selling price must be positive'),
    mrp: z.number().optional(),
    attributes: z.record(z.string()).optional(), // e.g., { size: "Large", color: "Red" }
    initialStock: z.number().min(0).default(0)
  })

  const validatedData = variantSchema.parse(body)

  // Check if product exists and belongs to store
  const product = await prisma.product.findFirst({
    where: {
      id: productId,
      storeId,
      isActive: true
    }
  })

  if (!product) {
    return createErrorResponse('Product not found', 404)
  }

  // Check if SKU already exists
  const existingSku = await prisma.productVariant.findFirst({
    where: {
      sku: validatedData.sku,
      product: { storeId },
      isActive: true
    }
  })

  if (existingSku) {
    return createErrorResponse('SKU already exists', 400)
  }

  // Check if barcode already exists (if provided)
  if (validatedData.barcode) {
    const existingBarcode = await prisma.productVariant.findFirst({
      where: {
        barcode: validatedData.barcode,
        product: { storeId },
        isActive: true
      }
    })

    if (existingBarcode) {
      return createErrorResponse('Barcode already exists', 400)
    }
  }

  // Validate selling price vs cost price
  if (validatedData.sellingPrice < validatedData.costPrice) {
    return createErrorResponse('Selling price cannot be less than cost price', 400)
  }

  const variant = await prisma.productVariant.create({
    data: {
      name: validatedData.name,
      sku: validatedData.sku,
      barcode: validatedData.barcode,
      costPrice: validatedData.costPrice,
      sellingPrice: validatedData.sellingPrice,
      mrp: validatedData.mrp,
      attributes: validatedData.attributes,
      productId,
      isActive: true
    },
    include: {
      product: {
        select: {
          name: true,
          categoryId: true
        }
      }
    }
  })

  // Create initial inventory record for variant
  await prisma.inventory.create({
    data: {
      productId,
      variantId: variant.id,
      storeId,
      quantity: validatedData.initialStock,
      reorderLevel: product.minStock
    }
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'PRODUCT',
    `Created variant: ${variant.name} for product: ${variant.product.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(variant, 'Product variant created successfully')
})
