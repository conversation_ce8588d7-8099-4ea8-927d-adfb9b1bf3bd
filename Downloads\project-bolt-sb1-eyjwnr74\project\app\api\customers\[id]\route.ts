import { NextRequest, NextResponse } from 'next/server'
import { 
  withPermission, 
  createSuccessResponse, 
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const customerUpdateSchema = z.object({
  name: z.string().min(1, 'Customer name is required').optional(),
  phone: z.string().optional(),
  email: z.string().email().optional().or(z.literal('')),
  address: z.string().optional(),
  gstNumber: z.string().optional(),
  creditLimit: z.number().min(0).optional(),
  isActive: z.boolean().optional()
})

// GET /api/customers/[id] - Get single customer
export const GET = withPermission('CUSTOMER', 'READ', async (request: NextRequest, user: any, { params }: { params: { id: string } }) => {
  const storeId = validateStoreAccess(user)
  const { id } = params

  const customer = await prisma.customer.findFirst({
    where: {
      id,
      storeId
    },
    include: {
      _count: {
        select: {
          sales: true,
          b2bOrders: true
        }
      },
      sales: {
        select: {
          id: true,
          saleNo: true,
          totalAmount: true,
          status: true,
          createdAt: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      },
      b2bOrders: {
        select: {
          id: true,
          orderNo: true,
          totalAmount: true,
          status: true,
          createdAt: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      }
    }
  })

  if (!customer) {
    return createErrorResponse('Customer not found', 404)
  }

  return createSuccessResponse(customer)
})

// PUT /api/customers/[id] - Update customer
export const PUT = withPermission('CUSTOMER', 'UPDATE', async (request: NextRequest, user: any, { params }: { params: { id: string } }) => {
  const storeId = validateStoreAccess(user)
  const { id } = params
  const body = await request.json()
  const data = customerUpdateSchema.parse(body)

  // Check if customer exists
  const existingCustomer = await prisma.customer.findFirst({
    where: {
      id,
      storeId
    }
  })

  if (!existingCustomer) {
    return createErrorResponse('Customer not found', 404)
  }

  // Check if phone already exists (if phone is being updated)
  if (data.phone && data.phone !== existingCustomer.phone) {
    const phoneExists = await prisma.customer.findFirst({
      where: {
        phone: data.phone,
        storeId,
        isActive: true,
        id: { not: id }
      }
    })

    if (phoneExists) {
      return createErrorResponse('Customer with this phone number already exists', 400)
    }
  }

  // Check if email already exists (if email is being updated)
  if (data.email && data.email !== existingCustomer.email) {
    const emailExists = await prisma.customer.findFirst({
      where: {
        email: data.email,
        storeId,
        isActive: true,
        id: { not: id }
      }
    })

    if (emailExists) {
      return createErrorResponse('Customer with this email already exists', 400)
    }
  }

  const updatedCustomer = await prisma.customer.update({
    where: { id },
    data,
    include: {
      _count: {
        select: {
          sales: true,
          b2bOrders: true
        }
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'UPDATE',
    'CUSTOMER',
    `Updated customer: ${updatedCustomer.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(updatedCustomer, 'Customer updated successfully')
})

// DELETE /api/customers/[id] - Delete customer (soft delete)
export const DELETE = withPermission('CUSTOMER', 'DELETE', async (request: NextRequest, user: any, { params }: { params: { id: string } }) => {
  const storeId = validateStoreAccess(user)
  const { id } = params

  // Check if customer exists
  const existingCustomer = await prisma.customer.findFirst({
    where: {
      id,
      storeId
    },
    include: {
      _count: {
        select: {
          sales: true,
          b2bOrders: true
        }
      }
    }
  })

  if (!existingCustomer) {
    return createErrorResponse('Customer not found', 404)
  }

  // Check if customer has orders
  if (existingCustomer._count.sales > 0 || existingCustomer._count.b2bOrders > 0) {
    return createErrorResponse('Cannot delete customer with existing orders. Consider deactivating instead.', 400)
  }

  // Soft delete
  await prisma.customer.update({
    where: { id },
    data: { isActive: false }
  })

  // Create audit log
  await createAuditLog(
    'DELETE',
    'CUSTOMER',
    `Deleted customer: ${existingCustomer.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(null, 'Customer deleted successfully')
})
