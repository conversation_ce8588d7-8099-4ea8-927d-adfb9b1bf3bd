'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Shield, 
  Users, 
  Settings, 
  Search, 
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Save,
  RefreshCw
} from 'lucide-react'
import { ROLE_PERMISSIONS, hasPermission, getUserModules, getModulePermissions } from '@/lib/permissions'
import { Role, Module, Permission } from '@/lib/types'

interface ModuleInfo {
  name: string
  description: string
  whoUses: string[]
  useCases: string[]
}

const MODULE_INFO: Record<Module, ModuleInfo> = {
  USER: {
    name: 'User Management',
    description: 'All system users (founder, super admin, admin, staff, distributor)',
    whoUses: ['Founder', 'Super Admin', 'Admin'],
    useCases: ['Create staff/distributor accounts', 'Edit or suspend users', 'Assign users to stores or roles']
  },
  ROLE: {
    name: 'Role Management',
    description: 'Role & Permission management (RBAC)',
    whoUses: ['Founder'],
    useCases: ['Define what each role can access', 'Customize permissions for modules']
  },
  STORE: {
    name: 'Store Management',
    description: 'Create and manage physical store branches',
    whoUses: ['Founder', 'Super Admin'],
    useCases: ['Founder creates stores', 'Super Admin updates store status/details']
  },
  PRODUCT: {
    name: 'Product Management',
    description: 'Master list of all sellable products',
    whoUses: ['Admin', 'Staff'],
    useCases: ['Add/edit products', 'Upload products in bulk via Excel', 'Export product catalog']
  },
  CATEGORY: {
    name: 'Category Management',
    description: 'Product categories like Grocery, Beverages, etc.',
    whoUses: ['Admin'],
    useCases: ['Create "Snacks", "Fruits", "Bakery" categories', 'Update names or delete unused categories']
  },
  INVENTORY: {
    name: 'Inventory Management',
    description: 'Tracks stock in/out levels per store',
    whoUses: ['Admin', 'Staff'],
    useCases: ['View stock quantity', 'Update quantity after stock count', 'Export stock levels']
  },
  PURCHASE: {
    name: 'Purchase Management',
    description: 'Orders made to suppliers/distributors',
    whoUses: ['Admin', 'Distributor'],
    useCases: ['Create a PO (purchase order)', 'Distributor approves and dispatches']
  },
  PURCHASE_RETURN: {
    name: 'Purchase Return',
    description: 'Returned items to suppliers/distributors',
    whoUses: ['Admin', 'Distributor'],
    useCases: ['Return expired or damaged products']
  },
  SALES: {
    name: 'Sales Management',
    description: 'POS billing & sales transactions',
    whoUses: ['Staff', 'Admin'],
    useCases: ['Staff bills a walk-in customer', 'Admin prints daily sales report']
  },
  SALES_RETURN: {
    name: 'Sales Return',
    description: 'Items returned by customers',
    whoUses: ['Staff', 'Admin'],
    useCases: ['Process a customer return', 'Refund or adjust bill']
  },
  CUSTOMER: {
    name: 'Customer Management',
    description: 'Customer master data (optional)',
    whoUses: ['Admin', 'Staff'],
    useCases: ['Add loyal customers', 'Attach bills to registered users']
  },
  DISTRIBUTOR: {
    name: 'Distributor Management',
    description: 'Distributor/supplier assigned to a store',
    whoUses: ['Admin'],
    useCases: ['Add or update a vendor', 'View supplier history']
  },
  EXPENSE: {
    name: 'Expense Management',
    description: 'Daily/monthly expense tracking',
    whoUses: ['Admin'],
    useCases: ['Track store electricity, rent, salary, etc.', 'Generate monthly cost reports']
  },
  SUPPLIER: {
    name: 'Supplier Management',
    description: 'Supplier master (can be same as Distributor)',
    whoUses: ['Admin'],
    useCases: ['View supplier-wise product rates', 'Update supplier contact details']
  },
  PAYMENT: {
    name: 'Payment Management',
    description: 'Tracks in/out cash flows',
    whoUses: ['Admin'],
    useCases: ['Add vendor payments', 'Track payment dues']
  },
  B2B: {
    name: 'B2B Management',
    description: 'Manage wholesale (business-to-business) clients',
    whoUses: ['Admin'],
    useCases: ['Create a bulk order from a nearby Kirana store', 'Approve credit-based sales']
  },
  BILLING: {
    name: 'Billing Management',
    description: 'POS Invoice management',
    whoUses: ['Admin', 'Staff'],
    useCases: ['Print bills', 'View previous transactions']
  },
  TAX: {
    name: 'Tax Management',
    description: 'GST/VAT configuration',
    whoUses: ['Admin', 'Super Admin'],
    useCases: ['Apply tax slabs to products', 'Update tax rules when regulations change']
  },
  NOTIFICATION: {
    name: 'Notification Management',
    description: 'Alert/reminder system',
    whoUses: ['Admin'],
    useCases: ['Low-stock alert', 'Expiry alert for products']
  },
  DOCUMENT: {
    name: 'Document Management',
    description: 'Upload and manage store/supplier documents',
    whoUses: ['Admin', 'Staff'],
    useCases: ['Upload invoices, supplier GST files', 'Download purchase receipts']
  },
  SETTINGS: {
    name: 'Settings Management',
    description: 'Store and system-level configurations',
    whoUses: ['Founder', 'Super Admin'],
    useCases: ['Enable/disable loyalty system', 'Customize invoice templates']
  },
  REPORTS: {
    name: 'Reports Management',
    description: 'Financial, inventory, sales reports',
    whoUses: ['Admin', 'Super Admin'],
    useCases: ['Generate monthly sales summary', 'Export P&L report']
  },
  DASHBOARD: {
    name: 'Dashboard',
    description: 'Real-time statistics and charts',
    whoUses: ['All roles (different visibility)'],
    useCases: ['Track today\'s sales', 'View low stock notifications']
  },
  AUDIT_LOG: {
    name: 'Audit Log',
    description: 'Logs of who did what',
    whoUses: ['Founder', 'Super Admin'],
    useCases: ['Detect unauthorized changes', 'Track store staff activities']
  },
  SUBSCRIPTION: {
    name: 'Subscription Management',
    description: 'SaaS billing plan (per store)',
    whoUses: ['Founder'],
    useCases: ['Assign monthly/annual subscription plans', 'Enable/disable features based on plan']
  },
  FEEDBACK: {
    name: 'Feedback Management',
    description: 'Collect and view feedback from customers',
    whoUses: ['Admin'],
    useCases: ['Analyze customer satisfaction', 'Improve store experience']
  },
  SUPPORT: {
    name: 'Support Management',
    description: 'Store support/ticket system',
    whoUses: ['Admin', 'Super Admin'],
    useCases: ['Admin raises issues to support', 'View ticket resolutions']
  }
}

const ROLE_COLORS: Record<Role, string> = {
  FOUNDER: 'bg-purple-100 text-purple-800 border-purple-200',
  SUPER_ADMIN: 'bg-blue-100 text-blue-800 border-blue-200',
  ADMIN: 'bg-green-100 text-green-800 border-green-200',
  STAFF: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  DISTRIBUTOR: 'bg-orange-100 text-orange-800 border-orange-200'
}

export default function PermissionsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState<Role | 'ALL'>('ALL')
  const [selectedModule, setSelectedModule] = useState<Module | 'ALL'>('ALL')

  const roles: Role[] = ['FOUNDER', 'SUPER_ADMIN', 'ADMIN', 'STAFF', 'DISTRIBUTOR']
  const modules: Module[] = Object.keys(MODULE_INFO) as Module[]

  const filteredPermissions = ROLE_PERMISSIONS.filter(permission => {
    const matchesSearch = searchTerm === '' || 
      permission.module.toLowerCase().includes(searchTerm.toLowerCase()) ||
      MODULE_INFO[permission.module].name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = selectedRole === 'ALL' || permission.role === selectedRole
    const matchesModule = selectedModule === 'ALL' || permission.module === selectedModule
    
    return matchesSearch && matchesRole && matchesModule
  })

  const getPermissionColor = (permission: Permission) => {
    const colors: Record<Permission, string> = {
      CREATE: 'bg-green-100 text-green-800',
      READ: 'bg-blue-100 text-blue-800',
      UPDATE: 'bg-yellow-100 text-yellow-800',
      DELETE: 'bg-red-100 text-red-800',
      EXPORT: 'bg-purple-100 text-purple-800',
      IMPORT: 'bg-indigo-100 text-indigo-800',
      ASSIGN: 'bg-pink-100 text-pink-800',
      APPROVE: 'bg-emerald-100 text-emerald-800',
      REJECT: 'bg-rose-100 text-rose-800',
      MANAGE: 'bg-violet-100 text-violet-800',
      ACCESS_SETTINGS: 'bg-slate-100 text-slate-800',
      UPLOAD: 'bg-cyan-100 text-cyan-800',
      DOWNLOAD: 'bg-teal-100 text-teal-800',
      PRINT: 'bg-amber-100 text-amber-800',
      ARCHIVE: 'bg-gray-100 text-gray-800',
      RESTORE: 'bg-lime-100 text-lime-800'
    }
    return colors[permission] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Permission Management
          </h1>
          <p className="text-muted-foreground mt-2">
            Comprehensive Role-Based Access Control (RBAC) for 27 modules
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Custom Role
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="roles">Role Matrix</TabsTrigger>
          <TabsTrigger value="modules">Module Details</TabsTrigger>
          <TabsTrigger value="permissions">Permission Editor</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{roles.length}</div>
                <p className="text-xs text-muted-foreground">
                  From Founder to Staff
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Modules</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{modules.length}</div>
                <p className="text-xs text-muted-foreground">
                  Complete system coverage
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Permission Rules</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{ROLE_PERMISSIONS.length}</div>
                <p className="text-xs text-muted-foreground">
                  Active permission rules
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Permission Types</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">16</div>
                <p className="text-xs text-muted-foreground">
                  Different permission types
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Role Distribution</CardTitle>
              <CardDescription>
                Overview of permissions across different roles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {roles.map(role => {
                  const rolePermissions = ROLE_PERMISSIONS.filter(p => p.role === role)
                  const moduleCount = new Set(rolePermissions.map(p => p.module)).size
                  const totalPermissions = rolePermissions.reduce((acc, p) => acc + p.permissions.length, 0)
                  
                  return (
                    <div key={role} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Badge className={ROLE_COLORS[role]}>
                          {role.replace('_', ' ')}
                        </Badge>
                        <div>
                          <p className="font-medium">{moduleCount} modules</p>
                          <p className="text-sm text-muted-foreground">{totalPermissions} permissions</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Role Permission Matrix</CardTitle>
              <CardDescription>
                Complete overview of what each role can access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Label htmlFor="search">Search Modules</Label>
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="search"
                        placeholder="Search modules..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="role-filter">Filter by Role</Label>
                    <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as Role | 'ALL')}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ALL">All Roles</SelectItem>
                        {roles.map(role => (
                          <SelectItem key={role} value={role}>
                            {role.replace('_', ' ')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <ScrollArea className="h-[600px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Module</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Permissions</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPermissions.map((permission, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <div>
                              <p className="font-medium">{MODULE_INFO[permission.module].name}</p>
                              <p className="text-sm text-muted-foreground">
                                {MODULE_INFO[permission.module].description}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={ROLE_COLORS[permission.role]}>
                              {permission.role.replace('_', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {permission.permissions.map(perm => (
                                <Badge 
                                  key={perm} 
                                  variant="outline" 
                                  className={getPermissionColor(perm)}
                                >
                                  {perm}
                                </Badge>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="modules" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {modules.map(module => {
              const moduleInfo = MODULE_INFO[module]
              const modulePermissions = ROLE_PERMISSIONS.filter(p => p.module === module)
              
              return (
                <Card key={module} className="h-full">
                  <CardHeader>
                    <CardTitle className="text-lg">{moduleInfo.name}</CardTitle>
                    <CardDescription>{moduleInfo.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium">Who Uses:</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {moduleInfo.whoUses.map(user => (
                          <Badge key={user} variant="secondary" className="text-xs">
                            {user}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium">Use Cases:</Label>
                      <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                        {moduleInfo.useCases.slice(0, 2).map((useCase, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-blue-500 mt-1">•</span>
                            <span>{useCase}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium">Roles with Access:</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {modulePermissions.map(mp => (
                          <Badge key={mp.role} className={ROLE_COLORS[mp.role]} variant="outline">
                            {mp.role.replace('_', ' ')}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Permission Editor</CardTitle>
              <CardDescription>
                Customize permissions for each role and module
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Permission Editor</h3>
                <p className="text-muted-foreground mb-4">
                  Advanced permission editing interface coming soon
                </p>
                <Button>
                  <Settings className="h-4 w-4 mr-2" />
                  Configure Permissions
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
