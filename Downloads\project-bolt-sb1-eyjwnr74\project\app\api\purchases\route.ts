import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const purchaseItemSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  discount: z.number().min(0).default(0),
  taxRate: z.number().min(0).max(100).default(0)
})

const purchaseSchema = z.object({
  supplierId: z.string().min(1, 'Supplier is required'),
  purchaseDate: z.string().optional(),
  expectedDeliveryDate: z.string().optional(),
  items: z.array(purchaseItemSchema).min(1, 'At least one item is required'),
  discount: z.number().min(0).default(0),
  taxAmount: z.number().min(0).default(0),
  notes: z.string().optional()
})

// GET /api/purchases - Get all purchases with pagination and filters
export const GET = withPermission('PURCHASE', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)

  // Get URL parameters for additional filters
  const url = new URL(request.url)
  const startDate = url.searchParams.get('startDate')
  const endDate = url.searchParams.get('endDate')
  const status = url.searchParams.get('status')
  const supplierId = url.searchParams.get('supplierId')

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['purchaseNo']),
    ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
  }

  if (status) {
    where.status = status
  }

  if (supplierId) {
    where.supplierId = supplierId
  }

  // Get total count
  const total = await prisma.purchase.count({ where })

  // Get purchases with pagination
  const purchases = await prisma.purchase.findMany({
    where,
    include: {
      supplier: {
        select: {
          id: true,
          name: true,
          contactPerson: true,
          phone: true
        }
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              unit: true
            }
          }
        }
      },
      createdBy: {
        select: {
          id: true,
          name: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  return createPaginatedResponse(purchases, page, limit, total)
})

// POST /api/purchases - Create new purchase
export const POST = withPermission('PURCHASE', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = purchaseSchema.parse(body)

  // Start transaction
  const result = await prisma.$transaction(async (tx) => {
    // Validate supplier exists
    const supplier = await tx.supplier.findFirst({
      where: {
        id: data.supplierId,
        storeId,
        isActive: true
      }
    })

    if (!supplier) {
      throw new Error('Invalid supplier selected')
    }

    // Validate products exist
    const productIds = data.items.map(item => item.productId)
    const products = await tx.product.findMany({
      where: {
        id: { in: productIds },
        storeId,
        isActive: true
      }
    })

    if (products.length !== productIds.length) {
      throw new Error('One or more products not found')
    }

    // Generate purchase number
    const purchaseCount = await tx.purchase.count({ where: { storeId } })
    const purchaseNo = `PUR-${String(purchaseCount + 1).padStart(6, '0')}`

    // Calculate totals
    let subtotal = 0
    let totalTax = 0
    let totalDiscount = data.discount || 0

    const purchaseItems = data.items.map(item => {
      const itemTotal = item.quantity * item.unitPrice
      const itemDiscount = item.discount || 0
      const itemTaxableAmount = itemTotal - itemDiscount
      const itemTax = (itemTaxableAmount * item.taxRate) / 100

      subtotal += itemTotal
      totalTax += itemTax
      totalDiscount += itemDiscount

      return {
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: itemTaxableAmount + itemTax
      }
    })

    const finalTotal = subtotal - totalDiscount + totalTax

    // Create purchase
    const purchase = await tx.purchase.create({
      data: {
        purchaseNo,
        purchaseDate: data.purchaseDate ? new Date(data.purchaseDate) : new Date(),
        expectedDeliveryDate: data.expectedDeliveryDate ? new Date(data.expectedDeliveryDate) : undefined,
        supplierId: data.supplierId,
        subtotal,
        discount: totalDiscount,
        taxAmount: totalTax,
        totalAmount: finalTotal,
        status: 'PENDING',
        notes: data.notes,
        createdById: user.id,
        storeId,
        items: {
          create: purchaseItems
        }
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                unit: true
              }
            }
          }
        },
        supplier: true
      }
    })

    return purchase
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'PURCHASE',
    `Created purchase: ${result.purchaseNo} - Total: ₹${result.totalAmount}`,
    user.id,
    storeId
  )

  return createSuccessResponse(result, 'Purchase created successfully')
})
