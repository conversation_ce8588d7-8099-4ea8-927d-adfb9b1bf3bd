"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/[id]/route";
exports.ids = ["app/api/users/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_DELL_Downloads_project_bolt_sb1_eyjwnr74_project_app_api_users_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/users/[id]/route.ts */ \"(rsc)/./app/api/users/[id]/route.ts\");\n\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst AppRouteRouteModule = next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule;\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/users/[id]/route\",\n        pathname: \"/api/users/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-sb1-eyjwnr74\\\\project\\\\app\\\\api\\\\users\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_Downloads_project_bolt_sb1_eyjwnr74_project_app_api_users_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/users/[id]/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/users/[id]/route.ts":
/*!*************************************!*\
  !*** ./app/api/users/[id]/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT),\n/* harmony export */   resetPassword: () => (/* binding */ resetPassword),\n/* harmony export */   toggleStatus: () => (/* binding */ toggleStatus)\n/* harmony export */ });\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst userUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1, \"Name is required\").optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().email(\"Valid email is required\").optional(),\n    password: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(6, \"Password must be at least 6 characters\").optional(),\n    role: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"FOUNDER\",\n        \"SUPER_ADMIN\",\n        \"ADMIN\",\n        \"STAFF\",\n        \"DISTRIBUTOR\"\n    ]).optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_2__.z.boolean().optional(),\n    permissions: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.string()).optional()\n});\n// GET /api/users/[id] - Get single user\nconst GET = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"USER\", \"READ\", async (request, user, { params })=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const { id } = params;\n    const targetUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findFirst({\n        where: {\n            id,\n            storeId\n        },\n        select: {\n            id: true,\n            name: true,\n            email: true,\n            phone: true,\n            role: true,\n            isActive: true,\n            permissions: true,\n            lastLoginAt: true,\n            createdAt: true,\n            updatedAt: true,\n            _count: {\n                select: {\n                    sales: true,\n                    purchases: true,\n                    auditLogs: true\n                }\n            },\n            sales: {\n                select: {\n                    id: true,\n                    saleNo: true,\n                    totalAmount: true,\n                    status: true,\n                    createdAt: true\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: 10\n            },\n            purchases: {\n                select: {\n                    id: true,\n                    purchaseNo: true,\n                    totalAmount: true,\n                    status: true,\n                    createdAt: true\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                },\n                take: 10\n            }\n        }\n    });\n    if (!targetUser) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"User not found\", 404);\n    }\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(targetUser);\n});\n// PUT /api/users/[id] - Update user\nconst PUT = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"USER\", \"UPDATE\", async (request, user, { params })=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const { id } = params;\n    const body = await request.json();\n    const data = userUpdateSchema.parse(body);\n    // Check if user exists\n    const existingUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findFirst({\n        where: {\n            id,\n            storeId\n        }\n    });\n    if (!existingUser) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"User not found\", 404);\n    }\n    // Check if email already exists (if email is being updated)\n    if (data.email && data.email !== existingUser.email) {\n        const emailExists = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findFirst({\n            where: {\n                email: data.email,\n                storeId,\n                id: {\n                    not: id\n                }\n            }\n        });\n        if (emailExists) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"User with this email already exists\", 400);\n        }\n    }\n    // Prepare update data\n    const updateData = {};\n    if (data.name) updateData.name = data.name;\n    if (data.email) updateData.email = data.email;\n    if (data.role) updateData.role = data.role;\n    if (data.phone !== undefined) updateData.phone = data.phone;\n    if (data.isActive !== undefined) updateData.isActive = data.isActive;\n    if (data.permissions) updateData.permissions = data.permissions;\n    // Hash password if provided\n    if (data.password) {\n        updateData.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().hash(data.password, 12);\n    }\n    const updatedUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n        where: {\n            id\n        },\n        data: updateData,\n        select: {\n            id: true,\n            name: true,\n            email: true,\n            phone: true,\n            role: true,\n            isActive: true,\n            permissions: true,\n            lastLoginAt: true,\n            createdAt: true,\n            updatedAt: true,\n            _count: {\n                select: {\n                    sales: true,\n                    purchases: true,\n                    auditLogs: true\n                }\n            }\n        }\n    });\n    // Create audit log\n    const changes = Object.keys(updateData).filter((key)=>key !== \"password\");\n    await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createAuditLog)(\"UPDATE\", \"USER\", `Updated user: ${updatedUser.name} (${updatedUser.email}) - Changed: ${changes.join(\", \")}`, user.id, storeId);\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(updatedUser, \"User updated successfully\");\n});\n// DELETE /api/users/[id] - Delete user (soft delete)\nconst DELETE = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"USER\", \"DELETE\", async (request, user, { params })=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const { id } = params;\n    // Check if user exists\n    const existingUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findFirst({\n        where: {\n            id,\n            storeId\n        },\n        include: {\n            _count: {\n                select: {\n                    sales: true,\n                    purchases: true\n                }\n            }\n        }\n    });\n    if (!existingUser) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"User not found\", 404);\n    }\n    // Prevent deleting yourself\n    if (existingUser.id === user.id) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Cannot delete your own account\", 400);\n    }\n    // Prevent deleting founder\n    if (existingUser.role === \"FOUNDER\") {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Cannot delete founder account\", 400);\n    }\n    // Check if user has transactions\n    if (existingUser._count.sales > 0 || existingUser._count.purchases > 0) {\n        // Soft delete - deactivate instead of deleting\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                isActive: false\n            }\n        });\n        // Create audit log\n        await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createAuditLog)(\"DEACTIVATE\", \"USER\", `Deactivated user: ${existingUser.name} (${existingUser.email}) - Has ${existingUser._count.sales} sales and ${existingUser._count.purchases} purchases`, user.id, storeId);\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(null, \"User deactivated successfully (has transaction history)\");\n    } else {\n        // Hard delete if no transactions\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.delete({\n            where: {\n                id\n            }\n        });\n        // Create audit log\n        await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createAuditLog)(\"DELETE\", \"USER\", `Deleted user: ${existingUser.name} (${existingUser.email})`, user.id, storeId);\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(null, \"User deleted successfully\");\n    }\n});\n// POST /api/users/[id]/reset-password - Reset user password\nconst resetPassword = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"USER\", \"UPDATE\", async (request, user, { params })=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const { id } = params;\n    const body = await request.json();\n    const schema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        newPassword: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(6, \"Password must be at least 6 characters\")\n    });\n    const { newPassword } = schema.parse(body);\n    // Check if user exists\n    const existingUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findFirst({\n        where: {\n            id,\n            storeId\n        }\n    });\n    if (!existingUser) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"User not found\", 404);\n    }\n    // Hash new password\n    const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_3___default().hash(newPassword, 12);\n    // Update password\n    await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n        where: {\n            id\n        },\n        data: {\n            password: hashedPassword\n        }\n    });\n    // Create audit log\n    await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createAuditLog)(\"UPDATE\", \"USER\", `Reset password for user: ${existingUser.name} (${existingUser.email})`, user.id, storeId);\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(null, \"Password reset successfully\");\n});\n// POST /api/users/[id]/toggle-status - Toggle user active status\nconst toggleStatus = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withPermission)(\"USER\", \"UPDATE\", async (request, user, { params })=>{\n    const storeId = (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.validateStoreAccess)(user);\n    const { id } = params;\n    // Check if user exists\n    const existingUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findFirst({\n        where: {\n            id,\n            storeId\n        }\n    });\n    if (!existingUser) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"User not found\", 404);\n    }\n    // Prevent deactivating yourself\n    if (existingUser.id === user.id) {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Cannot deactivate your own account\", 400);\n    }\n    // Prevent deactivating founder\n    if (existingUser.role === \"FOUNDER\") {\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createErrorResponse)(\"Cannot deactivate founder account\", 400);\n    }\n    // Toggle status\n    const updatedUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n        where: {\n            id\n        },\n        data: {\n            isActive: !existingUser.isActive\n        },\n        select: {\n            id: true,\n            name: true,\n            email: true,\n            isActive: true\n        }\n    });\n    // Create audit log\n    await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createAuditLog)(updatedUser.isActive ? \"ACTIVATE\" : \"DEACTIVATE\", \"USER\", `${updatedUser.isActive ? \"Activated\" : \"Deactivated\"} user: ${updatedUser.name} (${updatedUser.email})`, user.id, storeId);\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createSuccessResponse)(updatedUser, `User ${updatedUser.isActive ? \"activated\" : \"deactivated\"} successfully`);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/users/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildDateRangeFilter: () => (/* binding */ buildDateRangeFilter),\n/* harmony export */   buildSearchFilter: () => (/* binding */ buildSearchFilter),\n/* harmony export */   createAuditLog: () => (/* binding */ createAuditLog),\n/* harmony export */   createErrorResponse: () => (/* binding */ createErrorResponse),\n/* harmony export */   createPaginatedResponse: () => (/* binding */ createPaginatedResponse),\n/* harmony export */   createResponse: () => (/* binding */ createResponse),\n/* harmony export */   createSuccessResponse: () => (/* binding */ createSuccessResponse),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   validateRequiredFields: () => (/* binding */ validateRequiredFields),\n/* harmony export */   validateStoreAccess: () => (/* binding */ validateStoreAccess),\n/* harmony export */   withPermission: () => (/* binding */ withPermission)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n// Standard API response wrapper\nfunction createResponse(data, status = 200) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(data, {\n        status\n    });\n}\n// Error response wrapper\nfunction createErrorResponse(message, status = 400) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        error: message\n    }, {\n        status\n    });\n}\n// Success response wrapper\nfunction createSuccessResponse(data, message) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: true,\n        data,\n        message\n    });\n}\n// Paginated response wrapper\nfunction createPaginatedResponse(data, page, limit, total) {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit),\n            hasNext: page * limit < total,\n            hasPrev: page > 1\n        }\n    });\n}\n// Permission-based API handler wrapper\nfunction withPermission(module, permission, handler) {\n    return async (request)=>{\n        try {\n            const permissionCheck = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.checkPermission)(request, module, permission);\n            if (permissionCheck.error) {\n                return createErrorResponse(permissionCheck.error, permissionCheck.status);\n            }\n            return await handler(request, permissionCheck.user);\n        } catch (error) {\n            console.error(`API Error in ${module}:`, error);\n            return createErrorResponse(\"Internal server error\", 500);\n        }\n    };\n}\n// Audit log helper\nasync function createAuditLog(action, module, details, userId, storeId) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.auditLog.create({\n            data: {\n                action,\n                module,\n                details,\n                userId,\n                storeId\n            }\n        });\n    } catch (error) {\n        console.error(\"Failed to create audit log:\", error);\n    }\n}\n// Pagination helper\nfunction getPaginationParams(request) {\n    const url = new URL(request.url);\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n    const search = url.searchParams.get(\"search\") || \"\";\n    const sortBy = url.searchParams.get(\"sortBy\") || \"createdAt\";\n    const sortOrder = url.searchParams.get(\"sortOrder\") || \"desc\";\n    return {\n        page: Math.max(1, page),\n        limit: Math.min(100, Math.max(1, limit)),\n        skip: (Math.max(1, page) - 1) * Math.min(100, Math.max(1, limit)),\n        search,\n        sortBy,\n        sortOrder: sortOrder === \"asc\" ? \"asc\" : \"desc\"\n    };\n}\n// Search filter helper\nfunction buildSearchFilter(search, fields) {\n    if (!search) return {};\n    return {\n        OR: fields.map((field)=>({\n                [field]: {\n                    contains: search,\n                    mode: \"insensitive\"\n                }\n            }))\n    };\n}\n// Date range filter helper\nfunction buildDateRangeFilter(startDate, endDate, field = \"createdAt\") {\n    const filter = {};\n    if (startDate) {\n        filter[field] = {\n            ...filter[field],\n            gte: new Date(startDate)\n        };\n    }\n    if (endDate) {\n        const end = new Date(endDate);\n        end.setHours(23, 59, 59, 999);\n        filter[field] = {\n            ...filter[field],\n            lte: end\n        };\n    }\n    return Object.keys(filter).length > 0 ? filter : {};\n}\n// Validation helper\nfunction validateRequiredFields(data, fields) {\n    const missing = fields.filter((field)=>!data[field]);\n    if (missing.length > 0) {\n        throw new Error(`Missing required fields: ${missing.join(\", \")}`);\n    }\n}\n// Store validation helper\nfunction validateStoreAccess(user) {\n    if (!user.storeId) {\n        throw new Error(\"User not associated with any store\");\n    }\n    return user.storeId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   createAuthResponse: () => (/* binding */ createAuthResponse),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nfunction signToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function getAuthUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return null;\n        }\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            include: {\n                store: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        return user;\n    } catch (error) {\n        console.error(\"Auth error:\", error);\n        return null;\n    }\n}\nfunction createAuthResponse(user, token) {\n    return {\n        user: {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role,\n            storeId: user.storeId,\n            store: user.store\n        },\n        token\n    };\n}\n// Permission-based middleware helper\nasync function checkPermission(request, module, permission) {\n    const user = await getAuthUser(request);\n    if (!user) {\n        return {\n            error: \"Unauthorized\",\n            status: 401\n        };\n    }\n    // Import permissions dynamically to avoid circular dependency\n    const { hasPermission } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_permissions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./permissions */ \"(rsc)/./lib/permissions.ts\"));\n    if (!hasPermission(user.role, module, permission)) {\n        return {\n            error: \"Insufficient permissions\",\n            status: 403\n        };\n    }\n    return {\n        user,\n        error: null\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvcHJpc21hLnRzPzk4MjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2F%5Bid%5D%2Froute&page=%2Fapi%2Fusers%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();