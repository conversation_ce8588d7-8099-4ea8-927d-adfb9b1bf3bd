import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const supplierUpdateSchema = z.object({
  name: z.string().min(1, 'Supplier name is required').optional(),
  phone: z.string().min(1, 'Phone number is required').optional(),
  address: z.string().min(1, 'Address is required').optional(),
  email: z.string().email().optional().or(z.literal('')),
  gstNumber: z.string().optional(),
  isActive: z.boolean().optional()
})

// GET /api/suppliers/[id] - Get single supplier
export const GET = withPermission('SUPPLIER', 'READ', async (request: NextRequest, user: any, { params }: { params: { id: string } }) => {
  const storeId = validateStoreAccess(user)
  const { id } = params

  const supplier = await prisma.supplier.findFirst({
    where: {
      id,
      storeId
    },
    include: {
      _count: {
        select: {
          purchases: true
        }
      },
      purchases: {
        select: {
          id: true,
          purchaseNo: true,
          totalAmount: true,
          status: true,
          createdAt: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      }
    }
  })

  if (!supplier) {
    return createErrorResponse('Supplier not found', 404)
  }

  return createSuccessResponse(supplier)
})

// PUT /api/suppliers/[id] - Update supplier
export const PUT = withPermission('SUPPLIER', 'UPDATE', async (request: NextRequest, user: any, { params }: { params: { id: string } }) => {
  const storeId = validateStoreAccess(user)
  const { id } = params
  const body = await request.json()
  const data = supplierUpdateSchema.parse(body)

  // Check if supplier exists
  const existingSupplier = await prisma.supplier.findFirst({
    where: {
      id,
      storeId
    }
  })

  if (!existingSupplier) {
    return createErrorResponse('Supplier not found', 404)
  }

  // Check if phone already exists (if phone is being updated)
  if (data.phone && data.phone !== existingSupplier.phone) {
    const phoneExists = await prisma.supplier.findFirst({
      where: {
        phone: data.phone,
        storeId,
        isActive: true,
        id: { not: id }
      }
    })

    if (phoneExists) {
      return createErrorResponse('Supplier with this phone number already exists', 400)
    }
  }

  // Check if email already exists (if email is being updated)
  if (data.email && data.email !== existingSupplier.email) {
    const emailExists = await prisma.supplier.findFirst({
      where: {
        email: data.email,
        storeId,
        isActive: true,
        id: { not: id }
      }
    })

    if (emailExists) {
      return createErrorResponse('Supplier with this email already exists', 400)
    }
  }

  const updatedSupplier = await prisma.supplier.update({
    where: { id },
    data,
    include: {
      _count: {
        select: {
          purchases: true
        }
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'UPDATE',
    'SUPPLIER',
    `Updated supplier: ${updatedSupplier.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(updatedSupplier, 'Supplier updated successfully')
})

// DELETE /api/suppliers/[id] - Delete supplier (soft delete)
export const DELETE = withPermission('SUPPLIER', 'DELETE', async (request: NextRequest, user: any, { params }: { params: { id: string } }) => {
  const storeId = validateStoreAccess(user)
  const { id } = params

  // Check if supplier exists
  const existingSupplier = await prisma.supplier.findFirst({
    where: {
      id,
      storeId
    },
    include: {
      _count: {
        select: {
          purchases: true
        }
      }
    }
  })

  if (!existingSupplier) {
    return createErrorResponse('Supplier not found', 404)
  }

  // Check if supplier has purchases
  if (existingSupplier._count.purchases > 0) {
    return createErrorResponse('Cannot delete supplier with existing purchases. Consider deactivating instead.', 400)
  }

  // Soft delete
  await prisma.supplier.update({
    where: { id },
    data: { isActive: false }
  })

  // Create audit log
  await createAuditLog(
    'DELETE',
    'SUPPLIER',
    `Deleted supplier: ${existingSupplier.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(null, 'Supplier deleted successfully')
})
