import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// POST /api/sales/pos/scan - Scan barcode for POS
export const POST = withPermission('SALES', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const { barcode } = body

  if (!barcode) {
    return createErrorResponse('Barcode is required', 400)
  }

  try {
    // Search for product by barcode or SKU
    const product = await prisma.product.findFirst({
      where: {
        OR: [
          { barcode: barcode },
          { sku: barcode }
        ],
        storeId,
        isActive: true
      },
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        },
        inventory: {
          where: { storeId },
          select: {
            quantity: true,
            reorderLevel: true
          }
        },
        variants: {
          where: { 
            OR: [
              { barcode: barcode },
              { sku: barcode }
            ],
            isActive: true 
          },
          include: {
            inventory: {
              where: { storeId },
              select: {
                quantity: true,
                reorderLevel: true
              }
            }
          }
        }
      }
    })

    if (!product) {
      return createErrorResponse('Product not found', 404)
    }

    // Check if barcode matches a variant
    const matchedVariant = product.variants.find(v => 
      v.barcode === barcode || v.sku === barcode
    )

    let productData: any = {
      id: product.id,
      name: product.name,
      sku: product.sku,
      barcode: product.barcode,
      unit: product.unit,
      sellingPrice: product.sellingPrice,
      costPrice: product.costPrice,
      taxRate: product.taxRate,
      category: product.category,
      inventory: product.inventory[0] || null
    }

    // If variant matched, use variant data
    if (matchedVariant) {
      productData = {
        ...productData,
        variant: {
          id: matchedVariant.id,
          name: matchedVariant.name,
          sku: matchedVariant.sku,
          barcode: matchedVariant.barcode,
          sellingPrice: matchedVariant.sellingPrice,
          costPrice: matchedVariant.costPrice,
          attributes: matchedVariant.attributes
        },
        sellingPrice: matchedVariant.sellingPrice,
        costPrice: matchedVariant.costPrice,
        inventory: matchedVariant.inventory[0] || null
      }
    }

    // Check stock status
    const inventory = productData.inventory
    const stockStatus = inventory ? (
      inventory.quantity === 0 ? 'out_of_stock' :
      inventory.quantity <= inventory.reorderLevel ? 'low_stock' : 'in_stock'
    ) : 'no_inventory'

    const response = {
      ...productData,
      stockStatus,
      availableQuantity: inventory?.quantity || 0,
      reorderLevel: inventory?.reorderLevel || 0,
      canSell: inventory && inventory.quantity > 0,
      warnings: []
    }

    // Add warnings
    if (stockStatus === 'out_of_stock') {
      response.warnings.push('Product is out of stock')
    } else if (stockStatus === 'low_stock') {
      response.warnings.push(`Low stock: Only ${inventory.quantity} units remaining`)
    } else if (stockStatus === 'no_inventory') {
      response.warnings.push('No inventory record found')
    }

    return createSuccessResponse(response, 'Product found')

  } catch (error) {
    console.error('Error scanning barcode:', error)
    return createErrorResponse('Failed to scan barcode', 500)
  }
})

// GET /api/sales/pos/scan - Quick product search for POS
export const GET = withPermission('SALES', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q') || ''
  const limit = parseInt(searchParams.get('limit') || '10')

  if (!query || query.length < 2) {
    return createErrorResponse('Search query must be at least 2 characters', 400)
  }

  try {
    const products = await prisma.product.findMany({
      where: {
        storeId,
        isActive: true,
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { sku: { contains: query, mode: 'insensitive' } },
          { barcode: { contains: query, mode: 'insensitive' } }
        ]
      },
      include: {
        category: {
          select: {
            id: true,
            name: true
          }
        },
        inventory: {
          where: { storeId },
          select: {
            quantity: true,
            reorderLevel: true
          }
        },
        variants: {
          where: { isActive: true },
          include: {
            inventory: {
              where: { storeId },
              select: {
                quantity: true,
                reorderLevel: true
              }
            }
          }
        }
      },
      take: limit,
      orderBy: [
        { name: 'asc' }
      ]
    })

    const searchResults = products.map(product => {
      const inventory = product.inventory[0]
      const stockStatus = inventory ? (
        inventory.quantity === 0 ? 'out_of_stock' :
        inventory.quantity <= inventory.reorderLevel ? 'low_stock' : 'in_stock'
      ) : 'no_inventory'

      const baseProduct = {
        id: product.id,
        name: product.name,
        sku: product.sku,
        barcode: product.barcode,
        unit: product.unit,
        sellingPrice: product.sellingPrice,
        costPrice: product.costPrice,
        taxRate: product.taxRate,
        category: product.category,
        stockStatus,
        availableQuantity: inventory?.quantity || 0,
        reorderLevel: inventory?.reorderLevel || 0,
        canSell: inventory && inventory.quantity > 0
      }

      // Include variants if any
      const variants = product.variants.map(variant => {
        const variantInventory = variant.inventory[0]
        const variantStockStatus = variantInventory ? (
          variantInventory.quantity === 0 ? 'out_of_stock' :
          variantInventory.quantity <= variantInventory.reorderLevel ? 'low_stock' : 'in_stock'
        ) : 'no_inventory'

        return {
          id: variant.id,
          name: variant.name,
          sku: variant.sku,
          barcode: variant.barcode,
          sellingPrice: variant.sellingPrice,
          costPrice: variant.costPrice,
          attributes: variant.attributes,
          stockStatus: variantStockStatus,
          availableQuantity: variantInventory?.quantity || 0,
          reorderLevel: variantInventory?.reorderLevel || 0,
          canSell: variantInventory && variantInventory.quantity > 0
        }
      })

      return {
        ...baseProduct,
        variants: variants.length > 0 ? variants : undefined
      }
    })

    return createSuccessResponse({
      products: searchResults,
      count: searchResults.length,
      query
    }, `Found ${searchResults.length} products`)

  } catch (error) {
    console.error('Error searching products:', error)
    return createErrorResponse('Failed to search products', 500)
  }
})
