import { NextRequest, NextResponse } from 'next/server'
import { 
  withPermission, 
  createSuccessResponse, 
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const inventoryAdjustmentSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  adjustmentType: z.enum(['IN', 'OUT', 'SET']),
  quantity: z.number().min(0, 'Quantity must be positive'),
  reason: z.string().min(1, 'Reason is required'),
  notes: z.string().optional()
})

// GET /api/inventory - Get all inventory with pagination and filters
export const GET = withPermission('INVENTORY', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  
  // Get URL parameters for additional filters
  const url = new URL(request.url)
  const lowStock = url.searchParams.get('lowStock') === 'true'
  const categoryId = url.searchParams.get('categoryId')

  // Build filters for products
  const productWhere: any = { 
    storeId,
    isActive: true,
    ...buildSearchFilter(search, ['name', 'sku', 'barcode'])
  }

  if (categoryId) {
    productWhere.categoryId = categoryId
  }

  // Get products with inventory
  const products = await prisma.product.findMany({
    where: productWhere,
    include: {
      category: {
        select: {
          id: true,
          name: true
        }
      },
      inventory: {
        where: { storeId },
        select: {
          quantity: true,
          reorderLevel: true,
          lastUpdated: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  // Filter by low stock if requested
  let filteredProducts = products
  if (lowStock) {
    filteredProducts = products.filter(product => {
      const inventory = product.inventory[0]
      return inventory && inventory.quantity <= product.minStock
    })
  }

  // Get total count
  const total = await prisma.product.count({ where: productWhere })

  // Transform data for response
  const inventoryData = filteredProducts.map(product => ({
    id: product.id,
    name: product.name,
    sku: product.sku,
    unit: product.unit,
    category: product.category,
    minStock: product.minStock,
    costPrice: product.costPrice,
    sellingPrice: product.sellingPrice,
    inventory: product.inventory[0] || { quantity: 0, reorderLevel: 0, lastUpdated: null },
    isLowStock: product.inventory[0] ? product.inventory[0].quantity <= product.minStock : true
  }))

  return createPaginatedResponse(inventoryData, page, limit, total)
})

// POST /api/inventory - Create inventory adjustment
export const POST = withPermission('INVENTORY', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = inventoryAdjustmentSchema.parse(body)

  // Start transaction
  const result = await prisma.$transaction(async (tx) => {
    // Validate product exists
    const product = await tx.product.findFirst({
      where: {
        id: data.productId,
        storeId,
        isActive: true
      },
      include: {
        inventory: {
          where: { storeId }
        }
      }
    })

    if (!product) {
      throw new Error('Product not found')
    }

    const currentInventory = product.inventory[0]
    if (!currentInventory) {
      throw new Error('Inventory record not found for this product')
    }

    let newQuantity: number

    switch (data.adjustmentType) {
      case 'IN':
        newQuantity = currentInventory.quantity + data.quantity
        break
      case 'OUT':
        newQuantity = Math.max(0, currentInventory.quantity - data.quantity)
        break
      case 'SET':
        newQuantity = data.quantity
        break
      default:
        throw new Error('Invalid adjustment type')
    }

    // Update inventory
    const updatedInventory = await tx.inventory.update({
      where: {
        productId_storeId: {
          productId: data.productId,
          storeId
        }
      },
      data: {
        quantity: newQuantity,
        lastUpdated: new Date()
      }
    })

    // Create inventory adjustment record
    const adjustment = await tx.inventoryAdjustment.create({
      data: {
        productId: data.productId,
        storeId,
        adjustmentType: data.adjustmentType,
        quantityBefore: currentInventory.quantity,
        quantityAfter: newQuantity,
        quantityChanged: data.adjustmentType === 'SET' 
          ? newQuantity - currentInventory.quantity 
          : data.adjustmentType === 'IN' 
            ? data.quantity 
            : -data.quantity,
        reason: data.reason,
        notes: data.notes,
        createdById: user.id
      },
      include: {
        product: {
          select: {
            name: true,
            sku: true
          }
        }
      }
    })

    return {
      adjustment,
      inventory: updatedInventory,
      product: {
        id: product.id,
        name: product.name,
        sku: product.sku
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'UPDATE',
    'INVENTORY',
    `${data.adjustmentType} adjustment for ${result.product.name}: ${data.quantity} units - ${data.reason}`,
    user.id,
    storeId
  )

  return createSuccessResponse(result, 'Inventory adjusted successfully')
})
