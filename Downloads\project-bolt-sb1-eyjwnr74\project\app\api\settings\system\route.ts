import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess,
  createAuditLog
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const systemConfigSchema = z.object({
  maintenance: z.object({
    enabled: z.boolean(),
    message: z.string().optional(),
    allowedRoles: z.array(z.string()).optional(),
    scheduledStart: z.string().optional(),
    scheduledEnd: z.string().optional()
  }).optional(),
  features: z.object({
    multiStore: z.boolean().optional(),
    advancedReports: z.boolean().optional(),
    apiAccess: z.boolean().optional(),
    customFields: z.boolean().optional(),
    integrations: z.boolean().optional(),
    mobileApp: z.boolean().optional()
  }).optional(),
  limits: z.object({
    maxUsers: z.number().min(1).optional(),
    maxProducts: z.number().min(1).optional(),
    maxTransactions: z.number().min(1).optional(),
    storageLimit: z.number().min(1).optional(), // in MB
    apiRateLimit: z.number().min(1).optional() // requests per minute
  }).optional(),
  integrations: z.object({
    email: z.object({
      provider: z.enum(['smtp', 'sendgrid', 'mailgun', 'ses']).optional(),
      config: z.record(z.any()).optional(),
      enabled: z.boolean().optional()
    }).optional(),
    sms: z.object({
      provider: z.enum(['twilio', 'aws-sns', 'textlocal']).optional(),
      config: z.record(z.any()).optional(),
      enabled: z.boolean().optional()
    }).optional(),
    payment: z.object({
      providers: z.array(z.object({
        name: z.string(),
        enabled: z.boolean(),
        config: z.record(z.any())
      })).optional()
    }).optional(),
    storage: z.object({
      provider: z.enum(['local', 's3', 'gcs', 'azure']).optional(),
      config: z.record(z.any()).optional()
    }).optional()
  }).optional(),
  security: z.object({
    passwordPolicy: z.object({
      minLength: z.number().min(6).max(50).optional(),
      requireUppercase: z.boolean().optional(),
      requireLowercase: z.boolean().optional(),
      requireNumbers: z.boolean().optional(),
      requireSpecialChars: z.boolean().optional(),
      expiryDays: z.number().min(0).optional()
    }).optional(),
    sessionPolicy: z.object({
      timeout: z.number().min(300).max(86400).optional(), // 5 minutes to 24 hours
      maxConcurrent: z.number().min(1).max(10).optional(),
      rememberMe: z.boolean().optional()
    }).optional(),
    ipWhitelist: z.array(z.string()).optional(),
    twoFactorAuth: z.object({
      enabled: z.boolean(),
      required: z.boolean().optional(),
      methods: z.array(z.enum(['sms', 'email', 'app'])).optional()
    }).optional()
  }).optional()
})

// GET /api/settings/system - Get system configuration
export const GET = withPermission('SYSTEM', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const section = searchParams.get('section')

  // Only founders and super admins can access system settings
  if (!['FOUNDER', 'SUPER_ADMIN'].includes(user.role)) {
    return createErrorResponse('Insufficient permissions to access system configuration', 403)
  }

  try {
    const where: any = {
      storeId,
      category: 'SYSTEM'
    }

    if (section) {
      where.key = { startsWith: section }
    }

    const systemSettings = await prisma.setting.findMany({
      where,
      orderBy: { key: 'asc' }
    })

    // Group settings by section
    const config = systemSettings.reduce((acc: any, setting) => {
      const [section, ...keyParts] = setting.key.split('.')
      const key = keyParts.join('.')
      
      if (!acc[section]) {
        acc[section] = {}
      }
      
      acc[section][key] = parseSettingValue(setting.value, setting.dataType)
      return acc
    }, {})

    // Add system status information
    const systemStatus = await getSystemStatus(storeId)

    return createSuccessResponse({
      config,
      status: systemStatus,
      lastUpdated: systemSettings.length > 0 
        ? Math.max(...systemSettings.map(s => new Date(s.updatedAt).getTime()))
        : null
    })

  } catch (error) {
    console.error('Error fetching system configuration:', error)
    return createErrorResponse('Failed to fetch system configuration', 500)
  }
})

// POST /api/settings/system - Update system configuration
export const POST = withPermission('SYSTEM', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  // Only founders can update system settings
  if (user.role !== 'FOUNDER') {
    return createErrorResponse('Only founders can update system configuration', 403)
  }

  const config = systemConfigSchema.parse(body)

  try {
    const updates = []
    const errors = []

    // Process each configuration section
    for (const [section, sectionConfig] of Object.entries(config)) {
      if (!sectionConfig) continue

      for (const [key, value] of Object.entries(sectionConfig)) {
        try {
          const settingKey = `${section}.${key}`
          const dataType = inferDataType(value)
          const serializedValue = serializeSettingValue(value, dataType)

          // Validate critical settings
          const validationResult = validateSystemSetting(section, key, value)
          if (!validationResult.isValid) {
            errors.push(`${settingKey}: ${validationResult.error}`)
            continue
          }

          const setting = await prisma.setting.upsert({
            where: {
              storeId_category_key: {
                storeId,
                category: 'SYSTEM',
                key: settingKey
              }
            },
            update: {
              value: serializedValue,
              dataType,
              updatedById: user.id
            },
            create: {
              storeId,
              category: 'SYSTEM',
              key: settingKey,
              value: serializedValue,
              dataType,
              createdById: user.id,
              updatedById: user.id
            }
          })

          updates.push({
            key: settingKey,
            value: parseSettingValue(setting.value, setting.dataType)
          })

          // Handle special system settings
          await handleSystemSettingChange(section, key, value, storeId)

        } catch (error) {
          errors.push(`${section}.${key}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }
    }

    // Create audit log
    await createAuditLog(
      'UPDATE',
      'SYSTEM_CONFIG',
      `Updated ${updates.length} system configuration settings`,
      user.id,
      storeId
    )

    return createSuccessResponse({
      updated: updates,
      errors,
      summary: {
        successful: updates.length,
        failed: errors.length
      }
    }, `System configuration updated: ${updates.length} successful, ${errors.length} failed`)

  } catch (error) {
    console.error('Error updating system configuration:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to update system configuration',
      500
    )
  }
})

// PUT /api/settings/system/maintenance - Toggle maintenance mode
export const PUT = withPermission('SYSTEM', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  if (user.role !== 'FOUNDER') {
    return createErrorResponse('Only founders can toggle maintenance mode', 403)
  }

  const maintenanceSchema = z.object({
    enabled: z.boolean(),
    message: z.string().optional(),
    allowedRoles: z.array(z.string()).default(['FOUNDER', 'SUPER_ADMIN']),
    duration: z.number().optional() // minutes
  })

  const { enabled, message, allowedRoles, duration } = maintenanceSchema.parse(body)

  try {
    const maintenanceConfig = {
      enabled,
      message: message || 'System is under maintenance. Please try again later.',
      allowedRoles,
      startTime: enabled ? new Date().toISOString() : null,
      endTime: enabled && duration ? new Date(Date.now() + duration * 60 * 1000).toISOString() : null
    }

    await prisma.setting.upsert({
      where: {
        storeId_category_key: {
          storeId,
          category: 'SYSTEM',
          key: 'maintenance'
        }
      },
      update: {
        value: JSON.stringify(maintenanceConfig),
        dataType: 'JSON',
        updatedById: user.id
      },
      create: {
        storeId,
        category: 'SYSTEM',
        key: 'maintenance',
        value: JSON.stringify(maintenanceConfig),
        dataType: 'JSON',
        createdById: user.id,
        updatedById: user.id
      }
    })

    // Create audit log
    await createAuditLog(
      'UPDATE',
      'SYSTEM_CONFIG',
      `${enabled ? 'Enabled' : 'Disabled'} maintenance mode`,
      user.id,
      storeId
    )

    // Notify all users about maintenance mode
    if (enabled) {
      await notifyMaintenanceMode(storeId, maintenanceConfig)
    }

    return createSuccessResponse({
      maintenance: maintenanceConfig
    }, `Maintenance mode ${enabled ? 'enabled' : 'disabled'}`)

  } catch (error) {
    console.error('Error toggling maintenance mode:', error)
    return createErrorResponse('Failed to toggle maintenance mode', 500)
  }
})

// Helper functions

function parseSettingValue(value: string, dataType: string): any {
  try {
    switch (dataType) {
      case 'NUMBER':
        return parseFloat(value)
      case 'BOOLEAN':
        return value === 'true' || value === '1'
      case 'JSON':
      case 'ARRAY':
        return JSON.parse(value)
      default:
        return value
    }
  } catch {
    return value
  }
}

function serializeSettingValue(value: any, dataType: string): string {
  switch (dataType) {
    case 'JSON':
    case 'ARRAY':
      return JSON.stringify(value)
    case 'BOOLEAN':
      return value ? 'true' : 'false'
    default:
      return String(value)
  }
}

function inferDataType(value: any): string {
  if (typeof value === 'boolean') return 'BOOLEAN'
  if (typeof value === 'number') return 'NUMBER'
  if (Array.isArray(value)) return 'ARRAY'
  if (typeof value === 'object' && value !== null) return 'JSON'
  return 'STRING'
}

function validateSystemSetting(section: string, key: string, value: any): { isValid: boolean; error?: string } {
  switch (`${section}.${key}`) {
    case 'limits.maxUsers':
      if (typeof value !== 'number' || value < 1 || value > 10000) {
        return { isValid: false, error: 'Max users must be between 1 and 10000' }
      }
      break

    case 'security.passwordPolicy.minLength':
      if (typeof value !== 'number' || value < 6 || value > 50) {
        return { isValid: false, error: 'Password minimum length must be between 6 and 50' }
      }
      break

    case 'security.sessionPolicy.timeout':
      if (typeof value !== 'number' || value < 300 || value > 86400) {
        return { isValid: false, error: 'Session timeout must be between 5 minutes and 24 hours' }
      }
      break

    case 'integrations.email.provider':
      if (!['smtp', 'sendgrid', 'mailgun', 'ses'].includes(value)) {
        return { isValid: false, error: 'Invalid email provider' }
      }
      break
  }

  return { isValid: true }
}

async function handleSystemSettingChange(section: string, key: string, value: any, storeId: string) {
  switch (`${section}.${key}`) {
    case 'features.multiStore':
      // Enable/disable multi-store features
      if (!value) {
        // Disable multi-store features
        await prisma.store.updateMany({
          where: { parentStoreId: storeId },
          data: { isActive: false }
        })
      }
      break

    case 'limits.maxUsers':
      // Check if current user count exceeds new limit
      const userCount = await prisma.user.count({
        where: { storeId, isActive: true }
      })
      
      if (userCount > value) {
        throw new Error(`Current user count (${userCount}) exceeds new limit (${value})`)
      }
      break

    case 'security.twoFactorAuth.enabled':
      if (value) {
        // Enable 2FA for all users
        await prisma.user.updateMany({
          where: { storeId },
          data: { twoFactorEnabled: true }
        })
      }
      break
  }
}

async function getSystemStatus(storeId: string) {
  const [userCount, productCount, transactionCount, storageUsed] = await Promise.all([
    prisma.user.count({ where: { storeId, isActive: true } }),
    prisma.product.count({ where: { storeId, isActive: true } }),
    prisma.sale.count({ where: { storeId } }),
    getStorageUsage(storeId)
  ])

  return {
    users: userCount,
    products: productCount,
    transactions: transactionCount,
    storage: {
      used: storageUsed,
      unit: 'MB'
    },
    uptime: process.uptime(),
    version: '1.0.0',
    lastBackup: await getLastBackupTime(storeId)
  }
}

async function getStorageUsage(storeId: string): Promise<number> {
  // Calculate storage usage from documents and other files
  const documents = await prisma.document.findMany({
    where: { storeId },
    select: { fileSize: true }
  })

  return documents.reduce((total, doc) => total + doc.fileSize, 0) / (1024 * 1024) // Convert to MB
}

async function getLastBackupTime(storeId: string): Promise<Date | null> {
  // This would integrate with your backup system
  // For now, return a placeholder
  return new Date()
}

async function notifyMaintenanceMode(storeId: string, config: any) {
  // Create notifications for all users
  const users = await prisma.user.findMany({
    where: { storeId, isActive: true },
    select: { id: true }
  })

  for (const user of users) {
    await prisma.notification.create({
      data: {
        title: 'Maintenance Mode Enabled',
        message: config.message,
        type: 'WARNING',
        category: 'SYSTEM',
        priority: 'HIGH',
        userId: user.id,
        storeId
      }
    })
  }
}
