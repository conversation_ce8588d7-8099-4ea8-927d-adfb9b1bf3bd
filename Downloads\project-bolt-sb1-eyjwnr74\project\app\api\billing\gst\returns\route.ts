import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const gstReturnSchema = z.object({
  returnType: z.enum(['GSTR1', 'GSTR3B', 'GSTR9']),
  period: z.string().min(1, 'Period is required'), // YYYY-MM format
  financialYear: z.string().min(1, 'Financial year is required') // YYYY-YY format
})

// GET /api/billing/gst/returns - Get GST return data
export const GET = withPermission('BILLING', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const returnType = searchParams.get('returnType') || 'GSTR3B'
  const period = searchParams.get('period') // YYYY-MM
  const financialYear = searchParams.get('financialYear') // YYYY-YY

  if (!period) {
    return createErrorResponse('Period is required', 400)
  }

  try {
    // Get store GST details
    const store = await prisma.store.findFirst({
      where: { id: storeId },
      select: {
        gstNumber: true,
        name: true,
        address: true,
        state: true
      }
    })

    if (!store || !store.gstNumber) {
      return createErrorResponse('Store GST number not found', 400)
    }

    // Parse period
    const [year, month] = period.split('-').map(Number)
    const startDate = new Date(year, month - 1, 1)
    const endDate = new Date(year, month, 0, 23, 59, 59)

    let returnData: any = {}

    switch (returnType) {
      case 'GSTR1':
        returnData = await generateGSTR1(storeId, startDate, endDate)
        break
      case 'GSTR3B':
        returnData = await generateGSTR3B(storeId, startDate, endDate)
        break
      case 'GSTR9':
        returnData = await generateGSTR9(storeId, startDate, endDate, financialYear)
        break
      default:
        return createErrorResponse('Invalid return type', 400)
    }

    const response = {
      storeDetails: {
        gstNumber: store.gstNumber,
        legalName: store.name,
        address: store.address,
        state: store.state
      },
      returnType,
      period: {
        month: month.toString().padStart(2, '0'),
        year: year.toString(),
        startDate,
        endDate
      },
      data: returnData,
      generatedAt: new Date()
    }

    return createSuccessResponse(response, 'GST return data generated successfully')

  } catch (error) {
    console.error('Error generating GST return:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to generate GST return',
      500
    )
  }
})

// Helper functions for different GST returns

async function generateGSTR1(storeId: string, startDate: Date, endDate: Date) {
  // GSTR-1: Outward supplies
  const [b2bSupplies, b2cSupplies, exports, nilRated, hsn] = await Promise.all([
    // B2B Supplies
    prisma.invoice.findMany({
      where: {
        storeId,
        createdAt: { gte: startDate, lte: endDate },
        status: { in: ['SENT', 'PAID'] },
        customer: {
          gstNumber: { not: null }
        }
      },
      include: {
        customer: {
          select: {
            gstNumber: true,
            name: true,
            address: true
          }
        },
        items: {
          include: {
            product: {
              select: {
                hsnCode: true
              }
            }
          }
        }
      }
    }),

    // B2C Supplies
    prisma.invoice.findMany({
      where: {
        storeId,
        createdAt: { gte: startDate, lte: endDate },
        status: { in: ['SENT', 'PAID'] },
        customer: {
          gstNumber: null
        }
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                hsnCode: true
              }
            }
          }
        }
      }
    }),

    // Exports (placeholder)
    [],

    // Nil rated supplies (placeholder)
    [],

    // HSN Summary
    prisma.invoiceItem.groupBy({
      by: ['hsnCode'],
      where: {
        invoice: {
          storeId,
          createdAt: { gte: startDate, lte: endDate },
          status: { in: ['SENT', 'PAID'] }
        }
      },
      _sum: {
        quantity: true,
        taxableAmount: true,
        cgstAmount: true,
        sgstAmount: true,
        igstAmount: true,
        cessAmount: true
      }
    })
  ])

  return {
    b2b: formatB2BSupplies(b2bSupplies),
    b2cl: formatB2CLSupplies(b2cSupplies.filter(inv => inv.totalAmount > 250000)),
    b2cs: formatB2CSSupplies(b2cSupplies.filter(inv => inv.totalAmount <= 250000)),
    exp: exports,
    nil: nilRated,
    hsn: formatHSNSummary(hsn)
  }
}

async function generateGSTR3B(storeId: string, startDate: Date, endDate: Date) {
  // GSTR-3B: Monthly return
  const [outwardSupplies, inputTaxCredit, interestPenalty] = await Promise.all([
    // Outward supplies
    prisma.invoice.aggregate({
      where: {
        storeId,
        createdAt: { gte: startDate, lte: endDate },
        status: { in: ['SENT', 'PAID'] }
      },
      _sum: {
        taxableAmount: true,
        cgstAmount: true,
        sgstAmount: true,
        igstAmount: true,
        cessAmount: true
      }
    }),

    // Input Tax Credit (from purchases)
    prisma.purchase.aggregate({
      where: {
        storeId,
        createdAt: { gte: startDate, lte: endDate },
        status: { in: ['COMPLETED', 'RECEIVED'] }
      },
      _sum: {
        taxAmount: true
      }
    }),

    // Interest and penalty (placeholder)
    { interest: 0, penalty: 0, fees: 0 }
  ])

  const taxLiability = {
    cgst: outwardSupplies._sum.cgstAmount || 0,
    sgst: outwardSupplies._sum.sgstAmount || 0,
    igst: outwardSupplies._sum.igstAmount || 0,
    cess: outwardSupplies._sum.cessAmount || 0
  }

  const itc = {
    cgst: (inputTaxCredit._sum.taxAmount || 0) * 0.5, // Simplified
    sgst: (inputTaxCredit._sum.taxAmount || 0) * 0.5,
    igst: 0,
    cess: 0
  }

  const netTaxLiability = {
    cgst: Math.max(0, taxLiability.cgst - itc.cgst),
    sgst: Math.max(0, taxLiability.sgst - itc.sgst),
    igst: Math.max(0, taxLiability.igst - itc.igst),
    cess: Math.max(0, taxLiability.cess - itc.cess)
  }

  return {
    outwardSupplies: {
      taxableValue: outwardSupplies._sum.taxableAmount || 0,
      ...taxLiability
    },
    inputTaxCredit: itc,
    netTaxLiability,
    interestAndPenalty: interestPenalty,
    totalTaxPayable: Object.values(netTaxLiability).reduce((sum, val) => sum + val, 0)
  }
}

async function generateGSTR9(storeId: string, startDate: Date, endDate: Date, financialYear?: string) {
  // GSTR-9: Annual return (simplified)
  if (!financialYear) {
    throw new Error('Financial year is required for GSTR-9')
  }

  const [fyStart, fyEnd] = getFinancialYearDates(financialYear)

  const [annualTurnover, taxPaid, itcClaimed] = await Promise.all([
    // Annual turnover
    prisma.invoice.aggregate({
      where: {
        storeId,
        createdAt: { gte: fyStart, lte: fyEnd },
        status: { in: ['SENT', 'PAID'] }
      },
      _sum: {
        totalAmount: true,
        taxableAmount: true,
        cgstAmount: true,
        sgstAmount: true,
        igstAmount: true,
        cessAmount: true
      }
    }),

    // Tax paid
    prisma.taxPayment.aggregate({
      where: {
        storeId,
        paymentDate: { gte: fyStart, lte: fyEnd }
      },
      _sum: {
        cgstAmount: true,
        sgstAmount: true,
        igstAmount: true,
        cessAmount: true
      }
    }),

    // ITC claimed
    prisma.purchase.aggregate({
      where: {
        storeId,
        createdAt: { gte: fyStart, lte: fyEnd },
        status: { in: ['COMPLETED', 'RECEIVED'] }
      },
      _sum: {
        taxAmount: true
      }
    })
  ])

  return {
    financialYear,
    annualTurnover: annualTurnover._sum.totalAmount || 0,
    taxableSupplies: annualTurnover._sum.taxableAmount || 0,
    taxCollected: {
      cgst: annualTurnover._sum.cgstAmount || 0,
      sgst: annualTurnover._sum.sgstAmount || 0,
      igst: annualTurnover._sum.igstAmount || 0,
      cess: annualTurnover._sum.cessAmount || 0
    },
    taxPaid: {
      cgst: taxPaid._sum.cgstAmount || 0,
      sgst: taxPaid._sum.sgstAmount || 0,
      igst: taxPaid._sum.igstAmount || 0,
      cess: taxPaid._sum.cessAmount || 0
    },
    itcClaimed: itcClaimed._sum.taxAmount || 0
  }
}

// Helper formatting functions

function formatB2BSupplies(invoices: any[]) {
  return invoices.map(invoice => ({
    ctin: invoice.customer.gstNumber,
    trdnm: invoice.customer.name,
    inv: [{
      inum: invoice.invoiceNo,
      idt: invoice.invoiceDate.toISOString().split('T')[0],
      val: invoice.totalAmount,
      pos: '27', // Place of supply code
      rchrg: invoice.reverseCharge ? 'Y' : 'N',
      itms: invoice.items.map((item: any) => ({
        num: 1,
        itm_det: {
          txval: item.taxableAmount,
          rt: item.cgstRate + item.sgstRate + item.igstRate,
          camt: item.cgstAmount,
          samt: item.sgstAmount,
          iamt: item.igstAmount,
          csamt: item.cessAmount
        }
      }))
    }]
  }))
}

function formatB2CLSupplies(invoices: any[]) {
  // B2C Large (>2.5L) supplies
  return invoices.map(invoice => ({
    pos: '27',
    sply_ty: 'INTRA',
    inv: [{
      inum: invoice.invoiceNo,
      idt: invoice.invoiceDate.toISOString().split('T')[0],
      val: invoice.totalAmount,
      itms: invoice.items.map((item: any) => ({
        num: 1,
        itm_det: {
          txval: item.taxableAmount,
          rt: item.cgstRate + item.sgstRate + item.igstRate,
          camt: item.cgstAmount,
          samt: item.sgstAmount,
          iamt: item.igstAmount,
          csamt: item.cessAmount
        }
      }))
    }]
  }))
}

function formatB2CSSupplies(invoices: any[]) {
  // B2C Small (<=2.5L) supplies - consolidated
  const consolidated = new Map()
  
  invoices.forEach(invoice => {
    invoice.items.forEach((item: any) => {
      const key = `${item.cgstRate + item.sgstRate + item.igstRate}_INTRA`
      const existing = consolidated.get(key) || {
        sply_ty: 'INTRA',
        rt: item.cgstRate + item.sgstRate + item.igstRate,
        typ: 'OE',
        pos: '27',
        txval: 0,
        camt: 0,
        samt: 0,
        iamt: 0,
        csamt: 0
      }
      
      existing.txval += item.taxableAmount
      existing.camt += item.cgstAmount
      existing.samt += item.sgstAmount
      existing.iamt += item.igstAmount
      existing.csamt += item.cessAmount
      
      consolidated.set(key, existing)
    })
  })
  
  return Array.from(consolidated.values())
}

function formatHSNSummary(hsnData: any[]) {
  return hsnData.map(hsn => ({
    hsn_sc: hsn.hsnCode || '0000',
    desc: 'Goods', // Would need product description
    uqc: 'NOS',
    qty: hsn._sum.quantity || 0,
    val: hsn._sum.taxableAmount || 0,
    txval: hsn._sum.taxableAmount || 0,
    camt: hsn._sum.cgstAmount || 0,
    samt: hsn._sum.sgstAmount || 0,
    iamt: hsn._sum.igstAmount || 0,
    csamt: hsn._sum.cessAmount || 0
  }))
}

function getFinancialYearDates(financialYear: string): [Date, Date] {
  const [startYear] = financialYear.split('-')
  const fyStart = new Date(parseInt(startYear), 3, 1) // April 1st
  const fyEnd = new Date(parseInt(startYear) + 1, 2, 31, 23, 59, 59) // March 31st
  return [fyStart, fyEnd]
}
