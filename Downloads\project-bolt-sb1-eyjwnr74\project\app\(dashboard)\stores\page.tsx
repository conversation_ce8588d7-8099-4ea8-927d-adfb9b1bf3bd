'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Store, 
  Plus, 
  Search, 
  Filter,
  Eye,
  Edit,
  Trash2,
  MapPin,
  Phone,
  Mail,
  Users,
  Package,
  ShoppingCart,
  TrendingUp,
  Clock,
  Settings
} from 'lucide-react'
import { toast } from 'sonner'

interface Store {
  id: string
  name: string
  address: string
  phone: string
  email: string
  gstNumber?: string
  region?: string
  storeType: 'RETAIL' | 'WHOLESALE' | 'HYBRID'
  managerName?: string
  managerPhone?: string
  isActive: boolean
  createdAt: string
  createdBy: {
    id: string
    name: string
    email: string
  }
  _count: {
    users: number
    products: number
    sales: number
    customers: number
    suppliers: number
  }
  metrics?: {
    todaySales: number
    monthSales: number
    lowStockCount: number
  }
}

export default function StoresPage() {
  const [stores, setStores] = useState<Store[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  const [selectedStore, setSelectedStore] = useState<Store | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)

  useEffect(() => {
    fetchStores()
  }, [searchTerm, statusFilter, typeFilter])

  const fetchStores = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        search: searchTerm,
        status: statusFilter,
        ...(typeFilter !== 'all' && { storeType: typeFilter })
      })

      const response = await fetch(`/api/stores?${params}`)
      const data = await response.json()

      if (data.success) {
        setStores(data.data.stores)
      } else {
        toast.error(data.message || 'Failed to fetch stores')
      }
    } catch (error) {
      toast.error('Failed to fetch stores')
    } finally {
      setLoading(false)
    }
  }

  const handleViewStore = async (store: Store) => {
    try {
      const response = await fetch(`/api/stores/${store.id}`)
      const data = await response.json()

      if (data.success) {
        setSelectedStore(data.data)
        setShowDetailsDialog(true)
      } else {
        toast.error(data.message || 'Failed to fetch store details')
      }
    } catch (error) {
      toast.error('Failed to fetch store details')
    }
  }

  const getStoreTypeColor = (type: string) => {
    switch (type) {
      case 'RETAIL':
        return 'bg-blue-100 text-blue-800'
      case 'WHOLESALE':
        return 'bg-green-100 text-green-800'
      case 'HYBRID':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Store className="h-8 w-8 text-blue-600" />
            Store Management
          </h1>
          <p className="text-muted-foreground mt-2">
            Manage your store branches and locations
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Store
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Store</DialogTitle>
              <DialogDescription>
                Add a new store branch to your network
              </DialogDescription>
            </DialogHeader>
            {/* Store creation form will be added here */}
            <div className="text-center py-8">
              <Store className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Store creation form coming soon</p>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search stores..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="RETAIL">Retail</SelectItem>
                <SelectItem value="WHOLESALE">Wholesale</SelectItem>
                <SelectItem value="HYBRID">Hybrid</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Stores Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : stores.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <Store className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No stores found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? 'Try adjusting your search criteria' : 'Get started by creating your first store'}
            </p>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Store
            </Button>
          </div>
        ) : (
          stores.map((store) => (
            <Card key={store.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{store.name}</CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getStoreTypeColor(store.storeType)}>
                        {store.storeType}
                      </Badge>
                      <Badge variant={store.isActive ? 'default' : 'secondary'}>
                        {store.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleViewStore(store)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{store.address}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{store.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{store.email}</span>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Users className="h-4 w-4 text-blue-600" />
                      <span className="font-semibold">{store._count.users}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">Users</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Package className="h-4 w-4 text-green-600" />
                      <span className="font-semibold">{store._count.products}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">Products</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <ShoppingCart className="h-4 w-4 text-purple-600" />
                      <span className="font-semibold">{store._count.sales}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">Sales</p>
                  </div>
                </div>

                {store.metrics && (
                  <div className="pt-4 border-t">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Today's Sales</p>
                        <p className="font-semibold text-green-600">
                          {formatCurrency(store.metrics.todaySales)}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Low Stock</p>
                        <p className="font-semibold text-orange-600">
                          {store.metrics.lowStockCount} items
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex gap-2 pt-4">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Store Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Store className="h-5 w-5" />
              {selectedStore?.name}
            </DialogTitle>
            <DialogDescription>
              Detailed store information and analytics
            </DialogDescription>
          </DialogHeader>
          
          {selectedStore && (
            <div className="space-y-6">
              {/* Store Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Store Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium">Address</Label>
                      <p className="text-sm text-muted-foreground">{selectedStore.address}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Contact</Label>
                      <p className="text-sm text-muted-foreground">{selectedStore.phone}</p>
                      <p className="text-sm text-muted-foreground">{selectedStore.email}</p>
                    </div>
                    {selectedStore.gstNumber && (
                      <div>
                        <Label className="text-sm font-medium">GST Number</Label>
                        <p className="text-sm text-muted-foreground">{selectedStore.gstNumber}</p>
                      </div>
                    )}
                    <div>
                      <Label className="text-sm font-medium">Type</Label>
                      <Badge className={getStoreTypeColor(selectedStore.storeType)}>
                        {selectedStore.storeType}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Quick Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold">{selectedStore._count.users}</p>
                        <p className="text-sm text-muted-foreground">Users</p>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <Package className="h-8 w-8 text-green-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold">{selectedStore._count.products}</p>
                        <p className="text-sm text-muted-foreground">Products</p>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <ShoppingCart className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold">{selectedStore._count.sales}</p>
                        <p className="text-sm text-muted-foreground">Sales</p>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <TrendingUp className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold">{selectedStore._count.customers}</p>
                        <p className="text-sm text-muted-foreground">Customers</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Additional details can be added here */}
              <div className="text-center py-4">
                <p className="text-muted-foreground">More detailed analytics and management options coming soon</p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
