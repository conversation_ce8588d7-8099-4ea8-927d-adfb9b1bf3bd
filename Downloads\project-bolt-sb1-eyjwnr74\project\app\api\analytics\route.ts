import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const analyticsRequestSchema = z.object({
  metrics: z.array(z.enum([
    'REVENUE_TRENDS',
    'CUSTOMER_ACQUISITION',
    'PRODUCT_PERFORMANCE',
    'INVENTORY_TURNOVER',
    'PROFIT_MARGINS',
    'SEASONAL_ANALYSIS',
    'COHORT_ANALYSIS',
    'FORECASTING'
  ])),
  period: z.object({
    startDate: z.string(),
    endDate: z.string(),
    granularity: z.enum(['HOUR', 'DAY', 'WEEK', 'MONTH', 'QUARTER']).default('DAY')
  }),
  compareWith: z.object({
    enabled: z.boolean().default(false),
    startDate: z.string().optional(),
    endDate: z.string().optional()
  }).optional(),
  filters: z.object({
    categories: z.array(z.string()).optional(),
    products: z.array(z.string()).optional(),
    customers: z.array(z.string()).optional(),
    regions: z.array(z.string()).optional()
  }).optional()
})

// GET /api/analytics - Get business intelligence analytics
export const GET = withPermission('ANALYTICS', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const metric = searchParams.get('metric')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const granularity = searchParams.get('granularity') || 'DAY'

  if (!metric || !startDate || !endDate) {
    return createErrorResponse('Missing required parameters: metric, startDate, endDate', 400)
  }

  try {
    let analyticsData: any = {}

    switch (metric) {
      case 'REVENUE_TRENDS':
        analyticsData = await getRevenueTrends(storeId, new Date(startDate), new Date(endDate), granularity)
        break
      case 'CUSTOMER_ACQUISITION':
        analyticsData = await getCustomerAcquisition(storeId, new Date(startDate), new Date(endDate), granularity)
        break
      case 'PRODUCT_PERFORMANCE':
        analyticsData = await getProductPerformance(storeId, new Date(startDate), new Date(endDate))
        break
      case 'INVENTORY_TURNOVER':
        analyticsData = await getInventoryTurnover(storeId, new Date(startDate), new Date(endDate))
        break
      case 'PROFIT_MARGINS':
        analyticsData = await getProfitMargins(storeId, new Date(startDate), new Date(endDate))
        break
      case 'SEASONAL_ANALYSIS':
        analyticsData = await getSeasonalAnalysis(storeId, new Date(startDate), new Date(endDate))
        break
      case 'COHORT_ANALYSIS':
        analyticsData = await getCohortAnalysis(storeId, new Date(startDate), new Date(endDate))
        break
      case 'FORECASTING':
        analyticsData = await getForecasting(storeId, new Date(startDate), new Date(endDate))
        break
      default:
        return createErrorResponse('Invalid metric type', 400)
    }

    return createSuccessResponse({
      metric,
      period: { startDate, endDate, granularity },
      data: analyticsData,
      generatedAt: new Date()
    })

  } catch (error) {
    console.error('Error generating analytics:', error)
    return createErrorResponse('Failed to generate analytics', 500)
  }
})

// POST /api/analytics - Generate comprehensive analytics report
export const POST = withPermission('ANALYTICS', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = analyticsRequestSchema.parse(body)

  try {
    const results: any = {}
    const startDate = new Date(data.period.startDate)
    const endDate = new Date(data.period.endDate)

    // Generate analytics for each requested metric
    for (const metric of data.metrics) {
      switch (metric) {
        case 'REVENUE_TRENDS':
          results[metric] = await getRevenueTrends(storeId, startDate, endDate, data.period.granularity)
          break
        case 'CUSTOMER_ACQUISITION':
          results[metric] = await getCustomerAcquisition(storeId, startDate, endDate, data.period.granularity)
          break
        case 'PRODUCT_PERFORMANCE':
          results[metric] = await getProductPerformance(storeId, startDate, endDate)
          break
        case 'INVENTORY_TURNOVER':
          results[metric] = await getInventoryTurnover(storeId, startDate, endDate)
          break
        case 'PROFIT_MARGINS':
          results[metric] = await getProfitMargins(storeId, startDate, endDate)
          break
        case 'SEASONAL_ANALYSIS':
          results[metric] = await getSeasonalAnalysis(storeId, startDate, endDate)
          break
        case 'COHORT_ANALYSIS':
          results[metric] = await getCohortAnalysis(storeId, startDate, endDate)
          break
        case 'FORECASTING':
          results[metric] = await getForecasting(storeId, startDate, endDate)
          break
      }
    }

    // Add comparison data if requested
    if (data.compareWith?.enabled && data.compareWith.startDate && data.compareWith.endDate) {
      const compareStartDate = new Date(data.compareWith.startDate)
      const compareEndDate = new Date(data.compareWith.endDate)
      
      results.comparison = {}
      for (const metric of data.metrics) {
        // Generate comparison data for each metric
        results.comparison[metric] = await generateComparisonData(
          storeId, metric, compareStartDate, compareEndDate, data.period.granularity
        )
      }
    }

    return createSuccessResponse({
      metrics: data.metrics,
      period: data.period,
      data: results,
      generatedAt: new Date()
    }, 'Analytics generated successfully')

  } catch (error) {
    console.error('Error generating comprehensive analytics:', error)
    return createErrorResponse('Failed to generate analytics', 500)
  }
})

// Analytics helper functions

async function getRevenueTrends(storeId: string, startDate: Date, endDate: Date, granularity: string) {
  const sales = await prisma.sale.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate, lte: endDate },
      status: 'COMPLETED'
    },
    select: {
      createdAt: true,
      total: true,
      taxAmount: true,
      discount: true
    },
    orderBy: { createdAt: 'asc' }
  })

  const trends = groupByPeriod(sales, granularity, (items) => ({
    revenue: items.reduce((sum, sale) => sum + sale.total, 0),
    tax: items.reduce((sum, sale) => sum + sale.taxAmount, 0),
    discount: items.reduce((sum, sale) => sum + sale.discount, 0),
    transactions: items.length,
    averageOrderValue: items.length > 0 ? items.reduce((sum, sale) => sum + sale.total, 0) / items.length : 0
  }))

  return {
    trends,
    summary: {
      totalRevenue: sales.reduce((sum, sale) => sum + sale.total, 0),
      totalTransactions: sales.length,
      averageOrderValue: sales.length > 0 ? sales.reduce((sum, sale) => sum + sale.total, 0) / sales.length : 0,
      growthRate: calculateGrowthRate(trends)
    }
  }
}

async function getCustomerAcquisition(storeId: string, startDate: Date, endDate: Date, granularity: string) {
  const customers = await prisma.customer.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate, lte: endDate }
    },
    select: {
      createdAt: true,
      totalPurchases: true
    },
    orderBy: { createdAt: 'asc' }
  })

  const acquisitionTrends = groupByPeriod(customers, granularity, (items) => ({
    newCustomers: items.length,
    totalValue: items.reduce((sum, customer) => sum + customer.totalPurchases, 0),
    averageValue: items.length > 0 ? items.reduce((sum, customer) => sum + customer.totalPurchases, 0) / items.length : 0
  }))

  return {
    trends: acquisitionTrends,
    summary: {
      totalNewCustomers: customers.length,
      acquisitionRate: calculateAcquisitionRate(acquisitionTrends),
      customerLifetimeValue: customers.length > 0 ? customers.reduce((sum, customer) => sum + customer.totalPurchases, 0) / customers.length : 0
    }
  }
}

async function getProductPerformance(storeId: string, startDate: Date, endDate: Date) {
  const saleItems = await prisma.saleItem.findMany({
    where: {
      sale: {
        storeId,
        createdAt: { gte: startDate, lte: endDate },
        status: 'COMPLETED'
      }
    },
    include: {
      product: {
        select: {
          id: true,
          name: true,
          sku: true,
          costPrice: true,
          sellingPrice: true,
          category: {
            select: { name: true }
          }
        }
      }
    }
  })

  const productStats = saleItems.reduce((acc: any, item) => {
    const productId = item.productId
    if (!acc[productId]) {
      acc[productId] = {
        product: item.product,
        totalQuantity: 0,
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        salesCount: 0
      }
    }
    
    const cost = item.quantity * item.product.costPrice
    const profit = item.totalPrice - cost
    
    acc[productId].totalQuantity += item.quantity
    acc[productId].totalRevenue += item.totalPrice
    acc[productId].totalCost += cost
    acc[productId].totalProfit += profit
    acc[productId].salesCount += 1
    
    return acc
  }, {})

  const performanceData = Object.values(productStats).map((stats: any) => ({
    ...stats,
    profitMargin: stats.totalRevenue > 0 ? (stats.totalProfit / stats.totalRevenue) * 100 : 0,
    averageOrderQuantity: stats.salesCount > 0 ? stats.totalQuantity / stats.salesCount : 0
  }))

  return {
    topByRevenue: performanceData.sort((a: any, b: any) => b.totalRevenue - a.totalRevenue).slice(0, 10),
    topByQuantity: performanceData.sort((a: any, b: any) => b.totalQuantity - a.totalQuantity).slice(0, 10),
    topByProfit: performanceData.sort((a: any, b: any) => b.totalProfit - a.totalProfit).slice(0, 10),
    lowPerformers: performanceData.sort((a: any, b: any) => a.totalRevenue - b.totalRevenue).slice(0, 10)
  }
}

async function getInventoryTurnover(storeId: string, startDate: Date, endDate: Date) {
  const [inventory, saleItems] = await Promise.all([
    prisma.inventory.findMany({
      where: { storeId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            costPrice: true
          }
        }
      }
    }),
    
    prisma.saleItem.findMany({
      where: {
        sale: {
          storeId,
          createdAt: { gte: startDate, lte: endDate },
          status: 'COMPLETED'
        }
      },
      select: {
        productId: true,
        quantity: true
      }
    })
  ])

  const turnoverData = inventory.map(inv => {
    const soldQuantity = saleItems
      .filter(item => item.productId === inv.productId)
      .reduce((sum, item) => sum + item.quantity, 0)
    
    const averageInventory = inv.quantity + (soldQuantity / 2) // Simplified calculation
    const turnoverRate = averageInventory > 0 ? soldQuantity / averageInventory : 0
    
    return {
      productId: inv.productId,
      productName: inv.product.name,
      currentStock: inv.quantity,
      soldQuantity,
      turnoverRate,
      daysToSellOut: turnoverRate > 0 ? 365 / turnoverRate : Infinity,
      stockValue: inv.quantity * inv.product.costPrice
    }
  })

  return {
    products: turnoverData.sort((a, b) => b.turnoverRate - a.turnoverRate),
    summary: {
      averageTurnoverRate: turnoverData.reduce((sum, item) => sum + item.turnoverRate, 0) / turnoverData.length,
      fastMoving: turnoverData.filter(item => item.turnoverRate > 12).length, // More than 12 times per year
      slowMoving: turnoverData.filter(item => item.turnoverRate < 2).length, // Less than 2 times per year
      totalStockValue: turnoverData.reduce((sum, item) => sum + item.stockValue, 0)
    }
  }
}

async function getProfitMargins(storeId: string, startDate: Date, endDate: Date) {
  const saleItems = await prisma.saleItem.findMany({
    where: {
      sale: {
        storeId,
        createdAt: { gte: startDate, lte: endDate },
        status: 'COMPLETED'
      }
    },
    include: {
      product: {
        select: {
          costPrice: true,
          category: {
            select: { name: true }
          }
        }
      }
    }
  })

  const marginAnalysis = saleItems.reduce((acc: any, item) => {
    const cost = item.quantity * item.product.costPrice
    const profit = item.totalPrice - cost
    const margin = item.totalPrice > 0 ? (profit / item.totalPrice) * 100 : 0
    
    const category = item.product.category.name
    if (!acc[category]) {
      acc[category] = {
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        itemCount: 0
      }
    }
    
    acc[category].totalRevenue += item.totalPrice
    acc[category].totalCost += cost
    acc[category].totalProfit += profit
    acc[category].itemCount += 1
    
    return acc
  }, {})

  const categoryMargins = Object.entries(marginAnalysis).map(([category, data]: [string, any]) => ({
    category,
    ...data,
    profitMargin: data.totalRevenue > 0 ? (data.totalProfit / data.totalRevenue) * 100 : 0
  }))

  return {
    byCategory: categoryMargins.sort((a, b) => b.profitMargin - a.profitMargin),
    overall: {
      totalRevenue: categoryMargins.reduce((sum, cat) => sum + cat.totalRevenue, 0),
      totalCost: categoryMargins.reduce((sum, cat) => sum + cat.totalCost, 0),
      totalProfit: categoryMargins.reduce((sum, cat) => sum + cat.totalProfit, 0),
      overallMargin: categoryMargins.reduce((sum, cat) => sum + cat.totalRevenue, 0) > 0 
        ? (categoryMargins.reduce((sum, cat) => sum + cat.totalProfit, 0) / categoryMargins.reduce((sum, cat) => sum + cat.totalRevenue, 0)) * 100 
        : 0
    }
  }
}

async function getSeasonalAnalysis(storeId: string, startDate: Date, endDate: Date) {
  const sales = await prisma.sale.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate, lte: endDate },
      status: 'COMPLETED'
    },
    select: {
      createdAt: true,
      total: true
    }
  })

  const seasonalData = sales.reduce((acc: any, sale) => {
    const date = new Date(sale.createdAt)
    const month = date.getMonth()
    const quarter = Math.floor(month / 3) + 1
    const dayOfWeek = date.getDay()
    const hour = date.getHours()

    // Monthly analysis
    if (!acc.monthly[month]) {
      acc.monthly[month] = { sales: 0, revenue: 0 }
    }
    acc.monthly[month].sales += 1
    acc.monthly[month].revenue += sale.total

    // Quarterly analysis
    if (!acc.quarterly[quarter]) {
      acc.quarterly[quarter] = { sales: 0, revenue: 0 }
    }
    acc.quarterly[quarter].sales += 1
    acc.quarterly[quarter].revenue += sale.total

    // Day of week analysis
    if (!acc.dayOfWeek[dayOfWeek]) {
      acc.dayOfWeek[dayOfWeek] = { sales: 0, revenue: 0 }
    }
    acc.dayOfWeek[dayOfWeek].sales += 1
    acc.dayOfWeek[dayOfWeek].revenue += sale.total

    // Hourly analysis
    if (!acc.hourly[hour]) {
      acc.hourly[hour] = { sales: 0, revenue: 0 }
    }
    acc.hourly[hour].sales += 1
    acc.hourly[hour].revenue += sale.total

    return acc
  }, {
    monthly: {},
    quarterly: {},
    dayOfWeek: {},
    hourly: {}
  })

  return seasonalData
}

async function getCohortAnalysis(storeId: string, startDate: Date, endDate: Date) {
  // Simplified cohort analysis - would need more complex implementation for full cohort tracking
  const customers = await prisma.customer.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate, lte: endDate }
    },
    include: {
      sales: {
        where: {
          status: 'COMPLETED'
        },
        select: {
          createdAt: true,
          total: true
        }
      }
    }
  })

  const cohorts = customers.reduce((acc: any, customer) => {
    const cohortMonth = customer.createdAt.toISOString().substring(0, 7) // YYYY-MM
    if (!acc[cohortMonth]) {
      acc[cohortMonth] = {
        customers: 0,
        totalRevenue: 0,
        averageOrderValue: 0,
        retentionRate: 0
      }
    }
    
    acc[cohortMonth].customers += 1
    const customerRevenue = customer.sales.reduce((sum, sale) => sum + sale.total, 0)
    acc[cohortMonth].totalRevenue += customerRevenue
    
    return acc
  }, {})

  return Object.entries(cohorts).map(([month, data]: [string, any]) => ({
    cohortMonth: month,
    ...data,
    averageOrderValue: data.customers > 0 ? data.totalRevenue / data.customers : 0
  }))
}

async function getForecasting(storeId: string, startDate: Date, endDate: Date) {
  // Simple linear regression forecasting based on historical trends
  const sales = await prisma.sale.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate, lte: endDate },
      status: 'COMPLETED'
    },
    select: {
      createdAt: true,
      total: true
    },
    orderBy: { createdAt: 'asc' }
  })

  const dailyRevenue = groupByPeriod(sales, 'DAY', (items) => 
    items.reduce((sum, sale) => sum + sale.total, 0)
  )

  // Simple trend calculation
  const trend = calculateTrend(dailyRevenue.map(d => d.value))
  const nextPeriodForecast = generateForecast(dailyRevenue, trend, 30) // 30 days forecast

  return {
    historicalTrend: trend,
    forecast: nextPeriodForecast,
    confidence: calculateForecastConfidence(dailyRevenue)
  }
}

// Utility functions

function groupByPeriod(data: any[], granularity: string, aggregator: (items: any[]) => any) {
  const groups = new Map()
  
  data.forEach(item => {
    const date = new Date(item.createdAt)
    let key: string
    
    switch (granularity) {
      case 'HOUR':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`
        break
      case 'WEEK':
        const weekStart = new Date(date)
        weekStart.setDate(date.getDate() - date.getDay())
        key = weekStart.toISOString().split('T')[0]
        break
      case 'MONTH':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        break
      case 'QUARTER':
        const quarter = Math.floor(date.getMonth() / 3) + 1
        key = `${date.getFullYear()}-Q${quarter}`
        break
      default: // DAY
        key = date.toISOString().split('T')[0]
    }
    
    if (!groups.has(key)) {
      groups.set(key, [])
    }
    groups.get(key).push(item)
  })

  return Array.from(groups.entries()).map(([period, items]) => ({
    period,
    ...aggregator(items)
  })).sort((a, b) => a.period.localeCompare(b.period))
}

function calculateGrowthRate(trends: any[]): number {
  if (trends.length < 2) return 0
  
  const firstValue = trends[0].revenue
  const lastValue = trends[trends.length - 1].revenue
  
  return firstValue > 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0
}

function calculateAcquisitionRate(trends: any[]): number {
  if (trends.length === 0) return 0
  
  return trends.reduce((sum, trend) => sum + trend.newCustomers, 0) / trends.length
}

function calculateTrend(values: number[]): number {
  if (values.length < 2) return 0
  
  const n = values.length
  const sumX = (n * (n - 1)) / 2
  const sumY = values.reduce((sum, val) => sum + val, 0)
  const sumXY = values.reduce((sum, val, index) => sum + (index * val), 0)
  const sumXX = (n * (n - 1) * (2 * n - 1)) / 6
  
  return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
}

function generateForecast(historicalData: any[], trend: number, days: number): any[] {
  const forecast = []
  const lastValue = historicalData[historicalData.length - 1]?.value || 0
  
  for (let i = 1; i <= days; i++) {
    const forecastValue = lastValue + (trend * i)
    forecast.push({
      period: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      value: Math.max(0, forecastValue)
    })
  }
  
  return forecast
}

function calculateForecastConfidence(historicalData: any[]): number {
  // Simplified confidence calculation based on data consistency
  if (historicalData.length < 7) return 0.3
  
  const values = historicalData.map(d => d.value)
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  const coefficient = variance > 0 ? Math.sqrt(variance) / mean : 0
  
  return Math.max(0.1, Math.min(0.9, 1 - coefficient))
}

async function generateComparisonData(storeId: string, metric: string, startDate: Date, endDate: Date, granularity: string) {
  // Generate comparison data for the specified metric and period
  switch (metric) {
    case 'REVENUE_TRENDS':
      return await getRevenueTrends(storeId, startDate, endDate, granularity)
    case 'CUSTOMER_ACQUISITION':
      return await getCustomerAcquisition(storeId, startDate, endDate, granularity)
    default:
      return null
  }
}
