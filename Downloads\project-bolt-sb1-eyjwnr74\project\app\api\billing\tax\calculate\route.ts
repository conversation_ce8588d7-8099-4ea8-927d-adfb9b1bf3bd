import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const taxCalculationSchema = z.object({
  customerId: z.string().optional(),
  customerGST: z.string().optional(),
  customerState: z.string().optional(),
  placeOfSupply: z.string().optional(),
  items: z.array(z.object({
    productId: z.string().optional(),
    description: z.string().min(1, 'Description is required'),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
    unitPrice: z.number().min(0, 'Unit price must be positive'),
    discount: z.number().min(0).default(0),
    discountType: z.enum(['AMOUNT', 'PERCENTAGE']).default('PERCENTAGE'),
    taxRate: z.number().min(0).max(100).default(0),
    hsnCode: z.string().optional(),
    cessRate: z.number().min(0).max(100).default(0)
  })).min(1, 'At least one item is required'),
  discount: z.number().min(0).default(0),
  discountType: z.enum(['AMOUNT', 'PERCENTAGE']).default('AMOUNT'),
  shippingCharges: z.number().min(0).default(0),
  packingCharges: z.number().min(0).default(0),
  otherCharges: z.number().min(0).default(0),
  reverseCharge: z.boolean().default(false)
})

// POST /api/billing/tax/calculate - Calculate tax for invoice/order
export const POST = withPermission('BILLING', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = taxCalculationSchema.parse(body)

  try {
    // Get store details for tax calculation
    const store = await prisma.store.findFirst({
      where: { id: storeId },
      select: {
        gstNumber: true,
        state: true,
        address: true
      }
    })

    if (!store) {
      return createErrorResponse('Store not found', 404)
    }

    // Get customer details if customerId provided
    let customer = null
    if (data.customerId) {
      customer = await prisma.customer.findFirst({
        where: { id: data.customerId, storeId },
        select: {
          gstNumber: true,
          address: true
        }
      })
    }

    // Determine tax scenario
    const taxScenario = determineTaxScenario(
      store,
      customer,
      data.customerGST,
      data.customerState,
      data.placeOfSupply
    )

    // Calculate taxes for each item
    let subtotal = 0
    let totalItemDiscount = 0
    let totalCGST = 0
    let totalSGST = 0
    let totalIGST = 0
    let totalCESS = 0

    const itemCalculations = data.items.map(item => {
      const itemSubtotal = item.quantity * item.unitPrice
      
      let itemDiscount = 0
      if (item.discountType === 'PERCENTAGE') {
        itemDiscount = (itemSubtotal * item.discount) / 100
      } else {
        itemDiscount = item.discount
      }

      const taxableAmount = itemSubtotal - itemDiscount
      const itemTaxes = calculateItemTaxes(taxableAmount, item.taxRate, item.cessRate, taxScenario)

      subtotal += itemSubtotal
      totalItemDiscount += itemDiscount
      totalCGST += itemTaxes.cgstAmount
      totalSGST += itemTaxes.sgstAmount
      totalIGST += itemTaxes.igstAmount
      totalCESS += itemTaxes.cessAmount

      return {
        ...item,
        itemSubtotal,
        itemDiscount,
        taxableAmount,
        ...itemTaxes,
        totalAmount: taxableAmount + itemTaxes.totalTax
      }
    })

    // Apply overall discount
    let overallDiscount = data.discount || 0
    if (data.discountType === 'PERCENTAGE') {
      overallDiscount = (subtotal * overallDiscount) / 100
    }

    const totalDiscount = totalItemDiscount + overallDiscount
    const taxableValue = subtotal - totalDiscount

    // Calculate taxes on additional charges
    const additionalCharges = data.shippingCharges + data.packingCharges + data.otherCharges
    const additionalChargesTax = calculateAdditionalChargesTax(additionalCharges, taxScenario)

    const totalTax = totalCGST + totalSGST + totalIGST + totalCESS + additionalChargesTax.totalTax
    const grossAmount = taxableValue + totalTax + additionalCharges
    
    // Calculate round off (to nearest rupee)
    const roundOff = Math.round(grossAmount) - grossAmount
    const finalAmount = grossAmount + roundOff

    const calculation = {
      taxScenario,
      items: itemCalculations,
      summary: {
        subtotal,
        totalItemDiscount,
        overallDiscount,
        totalDiscount,
        taxableValue,
        cgstAmount: totalCGST + additionalChargesTax.cgstAmount,
        sgstAmount: totalSGST + additionalChargesTax.sgstAmount,
        igstAmount: totalIGST + additionalChargesTax.igstAmount,
        cessAmount: totalCESS + additionalChargesTax.cessAmount,
        totalTax,
        shippingCharges: data.shippingCharges,
        packingCharges: data.packingCharges,
        otherCharges: data.otherCharges,
        additionalCharges,
        grossAmount,
        roundOff,
        finalAmount
      },
      taxBreakdown: {
        cgst: {
          rate: taxScenario.cgstRate,
          amount: totalCGST + additionalChargesTax.cgstAmount
        },
        sgst: {
          rate: taxScenario.sgstRate,
          amount: totalSGST + additionalChargesTax.sgstAmount
        },
        igst: {
          rate: taxScenario.igstRate,
          amount: totalIGST + additionalChargesTax.igstAmount
        },
        cess: {
          amount: totalCESS + additionalChargesTax.cessAmount
        }
      },
      compliance: {
        reverseCharge: data.reverseCharge,
        exemptionReason: taxScenario.exemptionReason,
        applicableSections: getApplicableSections(taxScenario)
      }
    }

    return createSuccessResponse(calculation, 'Tax calculation completed successfully')

  } catch (error) {
    console.error('Error calculating tax:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to calculate tax',
      400
    )
  }
})

// Helper functions

function determineTaxScenario(store: any, customer: any, customerGST?: string, customerState?: string, placeOfSupply?: string) {
  const storeState = store.state
  const storeGST = store.gstNumber
  
  // Determine customer state
  let custState = customerState
  if (!custState && customer?.address) {
    // Extract state from customer address (simplified)
    custState = extractStateFromAddress(customer.address)
  }
  if (!custState && placeOfSupply) {
    custState = placeOfSupply
  }

  // Determine if inter-state or intra-state
  const isInterState = custState && custState !== storeState
  const hasCustomerGST = !!(customerGST || customer?.gstNumber)

  let scenario = {
    type: '',
    isInterState,
    hasCustomerGST,
    cgstRate: 0,
    sgstRate: 0,
    igstRate: 0,
    exemptionReason: null as string | null
  }

  if (isInterState) {
    scenario.type = hasCustomerGST ? 'B2B_INTERSTATE' : 'B2C_INTERSTATE'
    scenario.igstRate = 1 // Will be multiplied by actual tax rate
  } else {
    scenario.type = hasCustomerGST ? 'B2B_INTRASTATE' : 'B2C_INTRASTATE'
    scenario.cgstRate = 0.5 // Will be multiplied by actual tax rate (half)
    scenario.sgstRate = 0.5 // Will be multiplied by actual tax rate (half)
  }

  return scenario
}

function calculateItemTaxes(taxableAmount: number, taxRate: number, cessRate: number, taxScenario: any) {
  let cgstAmount = 0
  let sgstAmount = 0
  let igstAmount = 0
  let cessAmount = 0

  if (taxRate > 0) {
    if (taxScenario.isInterState) {
      igstAmount = (taxableAmount * taxRate) / 100
    } else {
      const halfRate = taxRate / 2
      cgstAmount = (taxableAmount * halfRate) / 100
      sgstAmount = (taxableAmount * halfRate) / 100
    }
  }

  if (cessRate > 0) {
    cessAmount = (taxableAmount * cessRate) / 100
  }

  return {
    cgstRate: taxScenario.isInterState ? 0 : taxRate / 2,
    cgstAmount,
    sgstRate: taxScenario.isInterState ? 0 : taxRate / 2,
    sgstAmount,
    igstRate: taxScenario.isInterState ? taxRate : 0,
    igstAmount,
    cessRate,
    cessAmount,
    totalTax: cgstAmount + sgstAmount + igstAmount + cessAmount
  }
}

function calculateAdditionalChargesTax(additionalCharges: number, taxScenario: any) {
  // Simplified: Apply 18% GST on additional charges
  const taxRate = 18
  let cgstAmount = 0
  let sgstAmount = 0
  let igstAmount = 0

  if (additionalCharges > 0) {
    if (taxScenario.isInterState) {
      igstAmount = (additionalCharges * taxRate) / 100
    } else {
      const halfRate = taxRate / 2
      cgstAmount = (additionalCharges * halfRate) / 100
      sgstAmount = (additionalCharges * halfRate) / 100
    }
  }

  return {
    cgstAmount,
    sgstAmount,
    igstAmount,
    cessAmount: 0,
    totalTax: cgstAmount + sgstAmount + igstAmount
  }
}

function extractStateFromAddress(address: string): string | null {
  // Simplified state extraction - in real implementation, use proper address parsing
  const states = [
    'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh',
    'Goa', 'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka',
    'Kerala', 'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram',
    'Nagaland', 'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 'Tamil Nadu',
    'Telangana', 'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal',
    'Delhi', 'Jammu and Kashmir', 'Ladakh', 'Puducherry', 'Chandigarh',
    'Andaman and Nicobar Islands', 'Dadra and Nagar Haveli and Daman and Diu',
    'Lakshadweep'
  ]

  for (const state of states) {
    if (address.toLowerCase().includes(state.toLowerCase())) {
      return state
    }
  }

  return null
}

function getApplicableSections(taxScenario: any): string[] {
  const sections = []

  if (taxScenario.isInterState) {
    sections.push('Section 5 - Inter-State Supply')
  } else {
    sections.push('Section 7 - Intra-State Supply')
  }

  if (!taxScenario.hasCustomerGST) {
    sections.push('Section 2(6) - Business to Consumer')
  }

  return sections
}
