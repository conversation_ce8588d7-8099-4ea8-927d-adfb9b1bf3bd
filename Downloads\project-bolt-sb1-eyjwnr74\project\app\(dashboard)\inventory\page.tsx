'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import {
  Package,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  RotateCcw,
  Plus,
  Minus,
  Settings
} from 'lucide-react'

interface InventoryItem {
  id: string
  name: string
  sku: string
  unit: string
  category: {
    id: string
    name: string
  }
  minStock: number
  costPrice: number
  sellingPrice: number
  inventory: {
    quantity: number
    reorderLevel: number
    lastUpdated: string | null
  }
  isLowStock: boolean
}

interface Category {
  id: string
  name: string
}

export default function InventoryPage() {
  const { token } = useAuth()
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [search, setSearch] = useState('')
  const [showDialog, setShowDialog] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<InventoryItem | null>(null)
  const [adjustmentForm, setAdjustmentForm] = useState({
    adjustmentType: 'IN' as 'IN' | 'OUT' | 'SET',
    quantity: 0,
    reason: '',
    notes: ''
  })
  const [submitting, setSubmitting] = useState(false)
  const [filters, setFilters] = useState({
    lowStock: false,
    categoryId: 'all'
  })

  // Stats
  const [stats, setStats] = useState({
    totalProducts: 0,
    lowStockItems: 0,
    totalValue: 0,
    outOfStock: 0
  })

  const columns = [
    {
      key: 'name',
      label: 'Product',
      sortable: true,
      render: (value: string, row: InventoryItem) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-muted-foreground">SKU: {row.sku}</div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'Category',
      render: (value: any) => (
        <Badge variant="outline">{value.name}</Badge>
      )
    },
    {
      key: 'inventory',
      label: 'Stock',
      render: (value: any, row: InventoryItem) => (
        <div className="flex items-center gap-2">
          {row.isLowStock && <AlertTriangle className="h-4 w-4 text-orange-500" />}
          <div>
            <div className={`font-medium ${row.isLowStock ? 'text-orange-600' : ''}`}>
              {value.quantity} {row.unit}
            </div>
            <div className="text-xs text-muted-foreground">
              Min: {row.minStock} {row.unit}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'costPrice',
      label: 'Cost Price',
      render: (value: number) => `₹${value.toFixed(2)}`
    },
    {
      key: 'sellingPrice',
      label: 'Selling Price',
      render: (value: number) => `₹${value.toFixed(2)}`
    },
    {
      key: 'value',
      label: 'Stock Value',
      render: (value: any, row: InventoryItem) => (
        <div className="font-medium">
          ₹{(row.inventory.quantity * row.costPrice).toFixed(2)}
        </div>
      )
    },
    {
      key: 'lastUpdated',
      label: 'Last Updated',
      render: (value: any, row: InventoryItem) => (
        <div className="text-sm">
          {row.inventory.lastUpdated
            ? new Date(row.inventory.lastUpdated).toLocaleDateString()
            : 'Never'
          }
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: InventoryItem) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleAdjust(row)}
        >
          <Settings className="h-4 w-4" />
        </Button>
      )
    }
  ]

  const fetchInventory = async (page = 1, limit = 10, searchTerm = '', filterParams = filters) => {
    if (!token) return

    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        search: searchTerm
      })

      if (filterParams.lowStock) {
        params.append('lowStock', 'true')
      }

      if (filterParams.categoryId && filterParams.categoryId !== 'all') {
        params.append('categoryId', filterParams.categoryId)
      }

      const response = await fetch(`/api/inventory?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch inventory')
      }

      const result = await response.json()
      setInventory(result.data)
      setPagination(result.pagination)
    } catch (error) {
      console.error('Error fetching inventory:', error)
      toast.error('Failed to load inventory')
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    if (!token) return

    try {
      const response = await fetch('/api/categories', {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch categories')
      }

      const result = await response.json()
      setCategories(result.data)
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const fetchStats = async () => {
    if (!token) return

    try {
      const response = await fetch('/api/inventory', {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (response.ok) {
        const result = await response.json()
        const allItems = result.data || []

        setStats({
          totalProducts: allItems.length,
          lowStockItems: allItems.filter((item: InventoryItem) => item.isLowStock).length,
          totalValue: allItems.reduce((sum: number, item: InventoryItem) =>
            sum + (item.inventory.quantity * item.costPrice), 0),
          outOfStock: allItems.filter((item: InventoryItem) => item.inventory.quantity === 0).length
        })
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleAdjust = (product: InventoryItem) => {
    setSelectedProduct(product)
    setAdjustmentForm({
      adjustmentType: 'IN',
      quantity: 0,
      reason: '',
      notes: ''
    })
    setShowDialog(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!token || !selectedProduct) return

    try {
      setSubmitting(true)

      const response = await fetch('/api/inventory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          productId: selectedProduct.id,
          ...adjustmentForm
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to adjust inventory')
      }

      toast.success('Inventory adjusted successfully')
      setShowDialog(false)
      fetchInventory(pagination.page, pagination.limit, search, filters)
      fetchStats()
    } catch (error: any) {
      toast.error(error.message)
    } finally {
      setSubmitting(false)
    }
  }

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
    fetchInventory(1, pagination.limit, search, newFilters)
  }

  useEffect(() => {
    fetchInventory()
    fetchCategories()
    fetchStats()
  }, [token])

  return (
    <div className="p-6 space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">In inventory</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.lowStockItems}</div>
            <p className="text-xs text-muted-foreground">Need reorder</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.totalValue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">Total inventory value</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.outOfStock}</div>
            <p className="text-xs text-muted-foreground">Zero quantity</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="lowStock"
            checked={filters.lowStock}
            onChange={(e) => handleFilterChange({ ...filters, lowStock: e.target.checked })}
            className="rounded"
          />
          <Label htmlFor="lowStock">Show only low stock items</Label>
        </div>

        <Select
          value={filters.categoryId}
          onValueChange={(value) => handleFilterChange({ ...filters, categoryId: value })}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Inventory</h1>
          <p className="text-muted-foreground">Monitor and manage your stock levels</p>
        </div>
      </div>

      {/* Inventory Table */}
      <DataTable
        columns={columns}
        data={inventory}
        loading={loading}
        pagination={pagination}
        onPageChange={(page) => fetchInventory(page, pagination.limit, search, filters)}
        onLimitChange={(limit) => fetchInventory(1, limit, search, filters)}
        onSearch={(searchTerm) => {
          setSearch(searchTerm)
          fetchInventory(1, pagination.limit, searchTerm, filters)
        }}
        searchPlaceholder="Search products..."
        title=""
        showAdd={false}
      />

      {/* Inventory Adjustment Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adjust Inventory</DialogTitle>
            <DialogDescription>
              {selectedProduct && (
                <>
                  Adjust stock for {selectedProduct.name} (SKU: {selectedProduct.sku})
                  <br />
                  Current stock: {selectedProduct.inventory.quantity} {selectedProduct.unit}
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="adjustmentType">Adjustment Type</Label>
              <Select
                value={adjustmentForm.adjustmentType}
                onValueChange={(value: any) => setAdjustmentForm(prev => ({ ...prev, adjustmentType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="IN">
                    <div className="flex items-center gap-2">
                      <Plus className="h-4 w-4 text-green-500" />
                      Stock In (Add)
                    </div>
                  </SelectItem>
                  <SelectItem value="OUT">
                    <div className="flex items-center gap-2">
                      <Minus className="h-4 w-4 text-red-500" />
                      Stock Out (Remove)
                    </div>
                  </SelectItem>
                  <SelectItem value="SET">
                    <div className="flex items-center gap-2">
                      <RotateCcw className="h-4 w-4 text-blue-500" />
                      Set Quantity
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="quantity">
                {adjustmentForm.adjustmentType === 'SET' ? 'New Quantity' : 'Quantity'}
              </Label>
              <Input
                id="quantity"
                type="number"
                min="0"
                value={adjustmentForm.quantity}
                onChange={(e) => setAdjustmentForm(prev => ({
                  ...prev,
                  quantity: parseInt(e.target.value) || 0
                }))}
                placeholder="Enter quantity"
                required
              />
            </div>

            <div>
              <Label htmlFor="reason">Reason *</Label>
              <Select
                value={adjustmentForm.reason}
                onValueChange={(value) => setAdjustmentForm(prev => ({ ...prev, reason: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Purchase">Purchase</SelectItem>
                  <SelectItem value="Sale">Sale</SelectItem>
                  <SelectItem value="Damage">Damage</SelectItem>
                  <SelectItem value="Theft">Theft</SelectItem>
                  <SelectItem value="Expired">Expired</SelectItem>
                  <SelectItem value="Return">Return</SelectItem>
                  <SelectItem value="Transfer">Transfer</SelectItem>
                  <SelectItem value="Count Adjustment">Count Adjustment</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={adjustmentForm.notes}
                onChange={(e) => setAdjustmentForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Enter additional notes"
                rows={3}
              />
            </div>

            {selectedProduct && (
              <div className="bg-muted p-3 rounded">
                <div className="text-sm">
                  <div>Current: {selectedProduct.inventory.quantity} {selectedProduct.unit}</div>
                  <div className="font-medium">
                    New quantity will be: {
                      adjustmentForm.adjustmentType === 'SET'
                        ? adjustmentForm.quantity
                        : adjustmentForm.adjustmentType === 'IN'
                          ? selectedProduct.inventory.quantity + adjustmentForm.quantity
                          : Math.max(0, selectedProduct.inventory.quantity - adjustmentForm.quantity)
                    } {selectedProduct.unit}
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowDialog(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={submitting || !adjustmentForm.reason}>
                {submitting ? 'Adjusting...' : 'Adjust Inventory'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
