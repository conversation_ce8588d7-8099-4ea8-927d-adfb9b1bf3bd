/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/detect-node-es";
exports.ids = ["vendor-chunks/detect-node-es"];
exports.modules = {

/***/ "(ssr)/./node_modules/detect-node-es/es5/node.js":
/*!*************************************************!*\
  !*** ./node_modules/detect-node-es/es5/node.js ***!
  \*************************************************/
/***/ ((module) => {

eval("// Only Node.JS has a process variable that is of [[Class]] process\nmodule.exports.isNode = Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZGV0ZWN0LW5vZGUtZXMvZXM1L25vZGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxxQkFBcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ncm9jZXJ5LW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL2RldGVjdC1ub2RlLWVzL2VzNS9ub2RlLmpzP2E4YTgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gT25seSBOb2RlLkpTIGhhcyBhIHByb2Nlc3MgdmFyaWFibGUgdGhhdCBpcyBvZiBbW0NsYXNzXV0gcHJvY2Vzc1xubW9kdWxlLmV4cG9ydHMuaXNOb2RlID0gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKHR5cGVvZiBwcm9jZXNzICE9PSAndW5kZWZpbmVkJyA/IHByb2Nlc3MgOiAwKSA9PT0gJ1tvYmplY3QgcHJvY2Vzc10nO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/detect-node-es/es5/node.js\n");

/***/ })

};
;