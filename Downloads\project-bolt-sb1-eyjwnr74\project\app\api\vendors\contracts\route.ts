import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const contractSchema = z.object({
  vendorId: z.string().min(1, 'Vendor is required'),
  vendorType: z.enum(['SUPPLIER', 'DISTRIBUTOR']),
  contractNumber: z.string().optional(),
  title: z.string().min(1, 'Contract title is required'),
  description: z.string().optional(),
  contractType: z.enum(['PURCHASE', 'SUPPLY', 'DISTRIBUTION', 'SERVICE']),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  autoRenewal: z.boolean().default(false),
  renewalPeriod: z.number().optional(), // in months
  terms: z.object({
    paymentTerms: z.string(),
    deliveryTerms: z.string(),
    qualityStandards: z.string().optional(),
    penaltyClauses: z.string().optional(),
    terminationClauses: z.string().optional()
  }),
  financialTerms: z.object({
    totalValue: z.number().min(0),
    currency: z.string().default('INR'),
    paymentSchedule: z.enum(['ADVANCE', 'ON_DELIVERY', 'NET_30', 'NET_60', 'CUSTOM']),
    discountTerms: z.string().optional(),
    penaltyRate: z.number().min(0).max(100).optional()
  }),
  products: z.array(z.object({
    productId: z.string(),
    minQuantity: z.number().min(0).optional(),
    maxQuantity: z.number().min(0).optional(),
    unitPrice: z.number().min(0),
    discountRate: z.number().min(0).max(100).default(0)
  })).optional(),
  documents: z.array(z.object({
    name: z.string(),
    url: z.string(),
    type: z.string()
  })).optional(),
  status: z.enum(['DRAFT', 'ACTIVE', 'EXPIRED', 'TERMINATED']).default('DRAFT'),
  notes: z.string().optional()
})

// GET /api/vendors/contracts - Get all vendor contracts
export const GET = withPermission('VENDOR', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  const vendorType = searchParams.get('vendorType')
  const contractType = searchParams.get('contractType')
  const status = searchParams.get('status')
  const expiringIn = searchParams.get('expiringIn') // days

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['contractNumber', 'title', 'description'])
  }

  if (vendorType) {
    where.vendorType = vendorType
  }

  if (contractType) {
    where.contractType = contractType
  }

  if (status) {
    where.status = status
  }

  if (expiringIn) {
    const expiryDate = new Date()
    expiryDate.setDate(expiryDate.getDate() + parseInt(expiringIn))
    where.endDate = { lte: expiryDate }
    where.status = 'ACTIVE'
  }

  // Get total count
  const total = await prisma.vendorContract.count({ where })

  // Get contracts with pagination
  const contracts = await prisma.vendorContract.findMany({
    where,
    include: {
      supplier: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true
        }
      },
      distributor: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true
        }
      },
      products: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              unit: true
            }
          }
        }
      },
      createdBy: {
        select: {
          id: true,
          name: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  // Add calculated fields
  const contractsWithCalculations = contracts.map(contract => {
    const now = new Date()
    const endDate = new Date(contract.endDate)
    const daysToExpiry = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    
    return {
      ...contract,
      vendor: contract.vendorType === 'SUPPLIER' ? contract.supplier : contract.distributor,
      daysToExpiry,
      isExpiring: daysToExpiry <= 30 && daysToExpiry > 0,
      isExpired: daysToExpiry <= 0
    }
  })

  return createPaginatedResponse(contractsWithCalculations, page, limit, total)
})

// POST /api/vendors/contracts - Create new vendor contract
export const POST = withPermission('VENDOR', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = contractSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate vendor exists
      if (data.vendorType === 'SUPPLIER') {
        const supplier = await tx.supplier.findFirst({
          where: { id: data.vendorId, storeId, isActive: true }
        })
        if (!supplier) {
          throw new Error('Supplier not found')
        }
      } else {
        const distributor = await tx.distributor.findFirst({
          where: { id: data.vendorId, storeId, isActive: true }
        })
        if (!distributor) {
          throw new Error('Distributor not found')
        }
      }

      // Validate products if provided
      if (data.products && data.products.length > 0) {
        const productIds = data.products.map(p => p.productId)
        const products = await tx.product.findMany({
          where: { id: { in: productIds }, storeId, isActive: true }
        })
        if (products.length !== productIds.length) {
          throw new Error('One or more products not found')
        }
      }

      // Generate contract number if not provided
      let contractNumber = data.contractNumber
      if (!contractNumber) {
        const contractCount = await tx.vendorContract.count({ where: { storeId } })
        contractNumber = `CON-${String(contractCount + 1).padStart(6, '0')}`
      }

      // Validate date range
      const startDate = new Date(data.startDate)
      const endDate = new Date(data.endDate)
      if (endDate <= startDate) {
        throw new Error('End date must be after start date')
      }

      // Create contract
      const contract = await tx.vendorContract.create({
        data: {
          ...data,
          contractNumber,
          startDate,
          endDate,
          supplierId: data.vendorType === 'SUPPLIER' ? data.vendorId : undefined,
          distributorId: data.vendorType === 'DISTRIBUTOR' ? data.vendorId : undefined,
          createdById: user.id,
          storeId
        }
      })

      // Create contract products if provided
      if (data.products && data.products.length > 0) {
        await tx.contractProduct.createMany({
          data: data.products.map(product => ({
            contractId: contract.id,
            productId: product.productId,
            minQuantity: product.minQuantity,
            maxQuantity: product.maxQuantity,
            unitPrice: product.unitPrice,
            discountRate: product.discountRate,
            storeId
          }))
        })
      }

      return contract
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'VENDOR_CONTRACT',
      `Created contract: ${result.contractNumber} - ${result.title}`,
      user.id,
      storeId
    )

    // Create notification for contract creation
    await prisma.notification.create({
      data: {
        title: 'New Contract Created',
        message: `Contract ${result.contractNumber} has been created and is now ${result.status}`,
        type: 'INFO',
        storeId,
        userId: user.id
      }
    })

    return createSuccessResponse(result, 'Contract created successfully')

  } catch (error) {
    console.error('Error creating contract:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create contract',
      400
    )
  }
})
