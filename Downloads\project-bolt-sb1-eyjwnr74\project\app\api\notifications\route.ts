import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const notificationSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  message: z.string().min(1, 'Message is required'),
  type: z.enum(['INFO', 'SUCCESS', 'WARNING', 'ERROR', 'ALERT', 'REMINDER']).default('INFO'),
  category: z.enum(['SYSTEM', 'INVENTORY', 'SALES', 'PURCHASE', 'CUSTOMER', 'FINANCIAL', 'SECURITY']).default('SYSTEM'),
  userId: z.string().optional(),
  customerId: z.string().optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  actionUrl: z.string().optional(),
  actionLabel: z.string().optional(),
  expiresAt: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  channels: z.array(z.enum(['IN_APP', 'EMAIL', 'SMS', 'PUSH'])).default(['IN_APP']),
  isScheduled: z.boolean().default(false),
  scheduledAt: z.string().optional(),
  recurring: z.object({
    enabled: z.boolean().default(false),
    frequency: z.enum(['DAILY', 'WEEKLY', 'MONTHLY']).optional(),
    endDate: z.string().optional()
  }).optional()
})

// GET /api/notifications - Get all notifications with advanced filtering
export const GET = withPermission('NOTIFICATION', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const type = searchParams.get('type')
  const category = searchParams.get('category')
  const priority = searchParams.get('priority')
  const isRead = searchParams.get('isRead')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const userId = searchParams.get('userId') || user.id // Default to current user

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['title', 'message']),
    ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
  }

  // Filter by user (either specific user or global notifications)
  where.OR = [
    { userId: userId },
    { userId: null } // Global notifications
  ]

  if (type) {
    where.type = type
  }

  if (category) {
    where.category = category
  }

  if (priority) {
    where.priority = priority
  }

  if (isRead !== null) {
    if (isRead === 'true') {
      where.readAt = { not: null }
    } else {
      where.readAt = null
    }
  }

  // Get total count
  const total = await prisma.notification.count({ where })

  // Get notifications with pagination
  const notifications = await prisma.notification.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          name: true
        }
      },
      customer: {
        select: {
          id: true,
          name: true,
          phone: true
        }
      },
      createdBy: {
        select: {
          id: true,
          name: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  // Add calculated fields
  const notificationsWithCalculations = notifications.map(notification => ({
    ...notification,
    isRead: !!notification.readAt,
    isExpired: notification.expiresAt && new Date(notification.expiresAt) < new Date(),
    timeAgo: getTimeAgo(notification.createdAt),
    canDismiss: !notification.isSystem || notification.priority !== 'URGENT'
  }))

  return createPaginatedResponse(notificationsWithCalculations, page, limit, total)
})

// POST /api/notifications - Create new notification
export const POST = withPermission('NOTIFICATION', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = notificationSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate user exists if userId provided
      if (data.userId) {
        const targetUser = await tx.user.findFirst({
          where: { id: data.userId, storeId, isActive: true }
        })
        if (!targetUser) {
          throw new Error('Target user not found')
        }
      }

      // Validate customer exists if customerId provided
      if (data.customerId) {
        const customer = await tx.customer.findFirst({
          where: { id: data.customerId, storeId, isActive: true }
        })
        if (!customer) {
          throw new Error('Customer not found')
        }
      }

      // Create notification
      const notification = await tx.notification.create({
        data: {
          title: data.title,
          message: data.message,
          type: data.type,
          category: data.category,
          userId: data.userId,
          customerId: data.customerId,
          priority: data.priority,
          actionUrl: data.actionUrl,
          actionLabel: data.actionLabel,
          expiresAt: data.expiresAt ? new Date(data.expiresAt) : undefined,
          metadata: data.metadata,
          channels: data.channels,
          isScheduled: data.isScheduled,
          scheduledAt: data.scheduledAt ? new Date(data.scheduledAt) : undefined,
          recurring: data.recurring,
          createdById: user.id,
          storeId
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          },
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          }
        }
      })

      // Send notification through specified channels
      if (!data.isScheduled) {
        await sendNotificationThroughChannels(notification, data.channels)
      }

      // Create recurring notifications if enabled
      if (data.recurring?.enabled && data.recurring.frequency) {
        await createRecurringNotifications(notification, data.recurring, storeId)
      }

      return notification
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'NOTIFICATION',
      `Created notification: ${result.title} - Priority: ${result.priority}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Notification created successfully')

  } catch (error) {
    console.error('Error creating notification:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create notification',
      400
    )
  }
})

// PUT /api/notifications/mark-read - Mark notifications as read
export const PUT = withPermission('NOTIFICATION', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  
  const markReadSchema = z.object({
    notificationIds: z.array(z.string()).optional(),
    markAll: z.boolean().default(false),
    category: z.string().optional()
  })

  const { notificationIds, markAll, category } = markReadSchema.parse(body)

  try {
    let where: any = {
      storeId,
      OR: [
        { userId: user.id },
        { userId: null }
      ],
      readAt: null
    }

    if (!markAll && notificationIds && notificationIds.length > 0) {
      where.id = { in: notificationIds }
    }

    if (category) {
      where.category = category
    }

    const updatedNotifications = await prisma.notification.updateMany({
      where,
      data: {
        readAt: new Date(),
        readById: user.id
      }
    })

    return createSuccessResponse({
      updatedCount: updatedNotifications.count
    }, `Marked ${updatedNotifications.count} notifications as read`)

  } catch (error) {
    console.error('Error marking notifications as read:', error)
    return createErrorResponse('Failed to mark notifications as read', 500)
  }
})

// Helper functions

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
  return date.toLocaleDateString()
}

async function sendNotificationThroughChannels(notification: any, channels: string[]) {
  for (const channel of channels) {
    try {
      switch (channel) {
        case 'EMAIL':
          if (notification.user?.email || notification.customer?.email) {
            await sendEmailNotification(notification)
          }
          break
        case 'SMS':
          if (notification.user?.phone || notification.customer?.phone) {
            await sendSMSNotification(notification)
          }
          break
        case 'PUSH':
          await sendPushNotification(notification)
          break
        case 'IN_APP':
          // Already stored in database
          break
      }
    } catch (error) {
      console.error(`Failed to send notification via ${channel}:`, error)
    }
  }
}

async function sendEmailNotification(notification: any) {
  // Email sending logic would go here
  // Integration with email service like SendGrid, AWS SES, etc.
  console.log('Sending email notification:', notification.title)
}

async function sendSMSNotification(notification: any) {
  // SMS sending logic would go here
  // Integration with SMS service like Twilio, AWS SNS, etc.
  console.log('Sending SMS notification:', notification.title)
}

async function sendPushNotification(notification: any) {
  // Push notification logic would go here
  // Integration with Firebase Cloud Messaging, etc.
  console.log('Sending push notification:', notification.title)
}

async function createRecurringNotifications(notification: any, recurring: any, storeId: string) {
  // Create scheduled notifications based on frequency
  const schedules = generateRecurringSchedule(recurring.frequency, recurring.endDate)
  
  for (const scheduleDate of schedules) {
    await prisma.notification.create({
      data: {
        ...notification,
        id: undefined, // Let Prisma generate new ID
        isScheduled: true,
        scheduledAt: scheduleDate,
        createdAt: undefined,
        updatedAt: undefined
      }
    })
  }
}

function generateRecurringSchedule(frequency: string, endDate?: string): Date[] {
  const schedules: Date[] = []
  const now = new Date()
  const end = endDate ? new Date(endDate) : new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000) // 1 year default

  let current = new Date(now)
  
  while (current <= end && schedules.length < 100) { // Limit to 100 schedules
    switch (frequency) {
      case 'DAILY':
        current.setDate(current.getDate() + 1)
        break
      case 'WEEKLY':
        current.setDate(current.getDate() + 7)
        break
      case 'MONTHLY':
        current.setMonth(current.getMonth() + 1)
        break
    }
    
    if (current <= end) {
      schedules.push(new Date(current))
    }
  }

  return schedules
}
