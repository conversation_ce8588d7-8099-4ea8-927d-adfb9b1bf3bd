import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// POST /api/purchases/[id]/approve - Approve purchase order
export const POST = withPermission('PURCHASE', 'APPROVE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const purchaseId = pathSegments[pathSegments.length - 2] // Get purchase ID from path
  const body = await request.json()

  const approvalSchema = z.object({
    notes: z.string().optional(),
    expectedDeliveryDate: z.string().optional(),
    adjustments: z.array(z.object({
      itemId: z.string(),
      newQuantity: z.number().min(0).optional(),
      newUnitPrice: z.number().min(0).optional(),
      reason: z.string().optional()
    })).optional()
  })

  const { notes, expectedDeliveryDate, adjustments } = approvalSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if purchase exists and belongs to store
      const purchase = await tx.purchase.findFirst({
        where: {
          id: purchaseId,
          storeId
        },
        include: {
          items: true,
          supplier: true
        }
      })

      if (!purchase) {
        throw new Error('Purchase not found')
      }

      if (purchase.status !== 'PENDING') {
        throw new Error(`Cannot approve purchase with status: ${purchase.status}`)
      }

      // Apply adjustments if provided
      let updatedTotal = purchase.totalAmount
      if (adjustments && adjustments.length > 0) {
        for (const adjustment of adjustments) {
          const item = purchase.items.find(i => i.id === adjustment.itemId)
          if (!item) continue

          const updateData: any = {}
          if (adjustment.newQuantity !== undefined) {
            updateData.quantity = adjustment.newQuantity
          }
          if (adjustment.newUnitPrice !== undefined) {
            updateData.unitPrice = adjustment.newUnitPrice
          }

          if (Object.keys(updateData).length > 0) {
            // Recalculate item total
            const newQuantity = adjustment.newQuantity ?? item.quantity
            const newUnitPrice = adjustment.newUnitPrice ?? item.unitPrice
            updateData.totalPrice = newQuantity * newUnitPrice

            await tx.purchaseItem.update({
              where: { id: adjustment.itemId },
              data: updateData
            })

            // Log adjustment
            await tx.purchaseAdjustment.create({
              data: {
                purchaseId,
                itemId: adjustment.itemId,
                originalQuantity: item.quantity,
                newQuantity: newQuantity,
                originalUnitPrice: item.unitPrice,
                newUnitPrice: newUnitPrice,
                reason: adjustment.reason || 'Approval adjustment',
                adjustedById: user.id,
                storeId
              }
            })
          }
        }

        // Recalculate total
        const updatedItems = await tx.purchaseItem.findMany({
          where: { purchaseId }
        })
        updatedTotal = updatedItems.reduce((sum, item) => sum + item.totalPrice, 0)
      }

      // Update purchase status
      const updatedPurchase = await tx.purchase.update({
        where: { id: purchaseId },
        data: {
          status: 'APPROVED',
          approvedById: user.id,
          approvedAt: new Date(),
          totalAmount: updatedTotal,
          ...(expectedDeliveryDate && { expectedDeliveryDate: new Date(expectedDeliveryDate) }),
          ...(notes && { approvalNotes: notes })
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  unit: true
                }
              }
            }
          },
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
              phone: true,
              email: true
            }
          },
          approvedBy: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Create notification for supplier (if email exists)
      if (purchase.supplier.email) {
        await tx.notification.create({
          data: {
            title: `Purchase Order Approved: ${purchase.purchaseNo}`,
            message: `Your purchase order ${purchase.purchaseNo} has been approved. Total amount: ₹${updatedTotal}`,
            type: 'INFO',
            recipientEmail: purchase.supplier.email,
            storeId
          }
        })
      }

      // Create notification for store users
      const storeUsers = await tx.user.findMany({
        where: {
          storeId,
          isActive: true,
          role: { in: ['FOUNDER', 'SUPER_ADMIN', 'ADMIN'] }
        },
        select: { id: true }
      })

      for (const storeUser of storeUsers) {
        await tx.notification.create({
          data: {
            title: `Purchase Order Approved`,
            message: `Purchase order ${purchase.purchaseNo} has been approved by ${user.name}`,
            type: 'SUCCESS',
            userId: storeUser.id,
            storeId
          }
        })
      }

      return updatedPurchase
    })

    // Create audit log
    await createAuditLog(
      'APPROVE',
      'PURCHASE',
      `Approved purchase: ${result.purchaseNo} - Total: ₹${result.totalAmount}${notes ? ` - Notes: ${notes}` : ''}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Purchase approved successfully')

  } catch (error) {
    console.error('Error approving purchase:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to approve purchase',
      400
    )
  }
})

// POST /api/purchases/[id]/reject - Reject purchase order
export const PUT = withPermission('PURCHASE', 'REJECT', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const purchaseId = pathSegments[pathSegments.length - 2] // Get purchase ID from path
  const body = await request.json()

  const rejectionSchema = z.object({
    reason: z.string().min(1, 'Rejection reason is required'),
    notes: z.string().optional()
  })

  const { reason, notes } = rejectionSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Check if purchase exists and belongs to store
      const purchase = await tx.purchase.findFirst({
        where: {
          id: purchaseId,
          storeId
        },
        include: {
          supplier: true
        }
      })

      if (!purchase) {
        throw new Error('Purchase not found')
      }

      if (purchase.status !== 'PENDING') {
        throw new Error(`Cannot reject purchase with status: ${purchase.status}`)
      }

      // Update purchase status
      const updatedPurchase = await tx.purchase.update({
        where: { id: purchaseId },
        data: {
          status: 'REJECTED',
          rejectedById: user.id,
          rejectedAt: new Date(),
          rejectionReason: reason,
          rejectionNotes: notes
        },
        include: {
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
              phone: true,
              email: true
            }
          },
          rejectedBy: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Create notification for supplier (if email exists)
      if (purchase.supplier.email) {
        await tx.notification.create({
          data: {
            title: `Purchase Order Rejected: ${purchase.purchaseNo}`,
            message: `Your purchase order ${purchase.purchaseNo} has been rejected. Reason: ${reason}`,
            type: 'ERROR',
            recipientEmail: purchase.supplier.email,
            storeId
          }
        })
      }

      return updatedPurchase
    })

    // Create audit log
    await createAuditLog(
      'REJECT',
      'PURCHASE',
      `Rejected purchase: ${result.purchaseNo} - Reason: ${reason}${notes ? ` - Notes: ${notes}` : ''}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Purchase rejected successfully')

  } catch (error) {
    console.error('Error rejecting purchase:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to reject purchase',
      400
    )
  }
})
