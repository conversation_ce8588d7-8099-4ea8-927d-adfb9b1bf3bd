import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const categoryUpdateSchema = z.object({
  name: z.string().min(1, 'Category name is required').optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional()
})

// GET /api/categories/[id] - Get single category
export const GET = withPermission('CATEGORY', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]

  const category = await prisma.category.findFirst({
    where: {
      id,
      storeId
    },
    include: {
      _count: {
        select: { products: true }
      },
      products: {
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          sku: true,
          sellingPrice: true,
          inventory: {
            select: {
              quantity: true
            }
          }
        },
        take: 10
      }
    }
  })

  if (!category) {
    return createErrorResponse('Category not found', 404)
  }

  return createSuccessResponse(category)
})

// PUT /api/categories/[id] - Update category
export const PUT = withPermission('CATEGORY', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]
  const body = await request.json()
  const data = categoryUpdateSchema.parse(body)

  // Check if category exists
  const existingCategory = await prisma.category.findFirst({
    where: {
      id,
      storeId
    }
  })

  if (!existingCategory) {
    return createErrorResponse('Category not found', 404)
  }

  // Check if name already exists (if name is being updated)
  if (data.name && data.name !== existingCategory.name) {
    const nameExists = await prisma.category.findFirst({
      where: {
        name: data.name,
        storeId,
        isActive: true,
        id: { not: id }
      }
    })

    if (nameExists) {
      return createErrorResponse('Category name already exists', 400)
    }
  }

  const updatedCategory = await prisma.category.update({
    where: { id },
    data,
    include: {
      _count: {
        select: { products: true }
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'UPDATE',
    'CATEGORY',
    `Updated category: ${updatedCategory.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(updatedCategory, 'Category updated successfully')
})

// DELETE /api/categories/[id] - Delete category (soft delete)
export const DELETE = withPermission('CATEGORY', 'DELETE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]

  // Check if category exists
  const existingCategory = await prisma.category.findFirst({
    where: {
      id,
      storeId
    },
    include: {
      _count: {
        select: { products: true }
      }
    }
  })

  if (!existingCategory) {
    return createErrorResponse('Category not found', 404)
  }

  // Check if category has products
  if (existingCategory._count.products > 0) {
    return createErrorResponse('Cannot delete category with existing products', 400)
  }

  // Soft delete
  await prisma.category.update({
    where: { id },
    data: { isActive: false }
  })

  // Create audit log
  await createAuditLog(
    'DELETE',
    'CATEGORY',
    `Deleted category: ${existingCategory.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(null, 'Category deleted successfully')
})
