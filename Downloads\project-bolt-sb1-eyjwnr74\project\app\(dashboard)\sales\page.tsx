'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import { 
  Plus, 
  Eye, 
  Trash2, 
  ShoppingCart, 
  CreditCard,
  Calendar,
  TrendingUp,
  DollarSign
} from 'lucide-react'

interface Sale {
  id: string
  saleNo: string
  customerId?: string
  customer?: {
    id: string
    name: string
    phone?: string
    email?: string
  }
  totalAmount: number
  taxAmount: number
  discount: number
  paidAmount: number
  paymentMethod: string
  status: string
  notes?: string
  createdAt: string
  items: Array<{
    id: string
    productId: string
    product: {
      id: string
      name: string
      sku: string
      unit: string
    }
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  createdBy: {
    id: string
    name: string
  }
}

interface Product {
  id: string
  name: string
  sku: string
  unit: string
  sellingPrice: number
  taxRate: number
  inventory: Array<{
    quantity: number
  }>
}

interface Customer {
  id: string
  name: string
  phone?: string
  email?: string
}

export default function SalesPage() {
  const { token } = useAuth()
  const [sales, setSales] = useState<Sale[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [search, setSearch] = useState('')
  const [showDialog, setShowDialog] = useState(false)
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null)
  const [submitting, setSubmitting] = useState(false)

  // Sale form state
  const [saleForm, setSaleForm] = useState({
    customerId: '',
    customerName: '',
    customerPhone: '',
    paymentMethod: 'CASH' as const,
    items: [] as Array<{
      productId: string
      quantity: number
      unitPrice: number
      discount: number
      taxRate: number
    }>,
    discount: 0,
    notes: ''
  })

  // Stats
  const [stats, setStats] = useState({
    todaySales: 0,
    todayAmount: 0,
    monthSales: 0,
    monthAmount: 0
  })

  const columns = [
    {
      key: 'saleNo',
      label: 'Sale No.',
      sortable: true,
      render: (value: string) => (
        <div className="font-mono text-sm">{value}</div>
      )
    },
    {
      key: 'customer',
      label: 'Customer',
      render: (value: any) => (
        <div>
          <div className="font-medium">{value?.name || 'Walk-in Customer'}</div>
          {value?.phone && (
            <div className="text-sm text-muted-foreground">{value.phone}</div>
          )}
        </div>
      )
    },
    {
      key: 'items',
      label: 'Items',
      render: (value: any[]) => (
        <Badge variant="secondary">
          {value.length} item{value.length !== 1 ? 's' : ''}
        </Badge>
      )
    },
    {
      key: 'totalAmount',
      label: 'Total',
      render: (value: number) => (
        <div className="font-medium">₹{value.toFixed(2)}</div>
      )
    },
    {
      key: 'paymentMethod',
      label: 'Payment',
      render: (value: string) => (
        <Badge variant="outline">
          <CreditCard className="h-3 w-3 mr-1" />
          {value}
        </Badge>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => (
        <Badge variant={value === 'COMPLETED' ? 'default' : 'secondary'}>
          {value}
        </Badge>
      )
    },
    {
      key: 'createdAt',
      label: 'Date',
      render: (value: string) => (
        <div className="text-sm">
          {new Date(value).toLocaleDateString()}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: Sale) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleView(row)}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ]

  const fetchSales = async (page = 1, limit = 10, searchTerm = '') => {
    if (!token) return

    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        search: searchTerm
      })

      const response = await fetch(`/api/sales?${params}`, {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch sales')
      }

      const result = await response.json()
      setSales(result.data)
      setPagination(result.pagination)
    } catch (error) {
      console.error('Error fetching sales:', error)
      toast.error('Failed to load sales')
    } finally {
      setLoading(false)
    }
  }

  const fetchProducts = async () => {
    if (!token) return

    try {
      const response = await fetch('/api/products', {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch products')
      }

      const result = await response.json()
      setProducts(result.data)
    } catch (error) {
      console.error('Error fetching products:', error)
    }
  }

  const fetchCustomers = async () => {
    if (!token) return

    try {
      const response = await fetch('/api/customers', {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch customers')
      }

      const result = await response.json()
      setCustomers(result.data || [])
    } catch (error) {
      console.error('Error fetching customers:', error)
    }
  }

  const fetchStats = async () => {
    if (!token) return

    try {
      const today = new Date().toISOString().split('T')[0]
      const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]
      
      // Fetch today's sales
      const todayResponse = await fetch(`/api/sales?startDate=${today}&endDate=${today}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      
      // Fetch month's sales
      const monthResponse = await fetch(`/api/sales?startDate=${monthStart}`, {
        headers: { Authorization: `Bearer ${token}` }
      })

      if (todayResponse.ok && monthResponse.ok) {
        const todayData = await todayResponse.json()
        const monthData = await monthResponse.json()

        setStats({
          todaySales: todayData.pagination?.total || 0,
          todayAmount: todayData.data?.reduce((sum: number, sale: Sale) => sum + sale.totalAmount, 0) || 0,
          monthSales: monthData.pagination?.total || 0,
          monthAmount: monthData.data?.reduce((sum: number, sale: Sale) => sum + sale.totalAmount, 0) || 0
        })
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleView = (sale: Sale) => {
    setSelectedSale(sale)
    setShowViewDialog(true)
  }

  const handleAdd = () => {
    setSaleForm({
      customerId: '',
      customerName: '',
      customerPhone: '',
      paymentMethod: 'CASH',
      items: [],
      discount: 0,
      notes: ''
    })
    setShowDialog(true)
  }

  const addItem = () => {
    setSaleForm(prev => ({
      ...prev,
      items: [...prev.items, {
        productId: '',
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        taxRate: 0
      }]
    }))
  }

  const removeItem = (index: number) => {
    setSaleForm(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  const updateItem = (index: number, field: string, value: any) => {
    setSaleForm(prev => ({
      ...prev,
      items: prev.items.map((item, i) => {
        if (i === index) {
          const updatedItem = { ...item, [field]: value }
          
          // Auto-fill price and tax when product is selected
          if (field === 'productId') {
            const product = products.find(p => p.id === value)
            if (product) {
              updatedItem.unitPrice = product.sellingPrice
              updatedItem.taxRate = product.taxRate
            }
          }
          
          return updatedItem
        }
        return item
      })
    }))
  }

  const calculateTotal = () => {
    const itemsTotal = saleForm.items.reduce((sum, item) => {
      const itemTotal = item.quantity * item.unitPrice
      const itemDiscount = item.discount || 0
      const taxableAmount = itemTotal - itemDiscount
      const tax = (taxableAmount * item.taxRate) / 100
      return sum + taxableAmount + tax
    }, 0)
    
    return Math.max(0, itemsTotal - saleForm.discount)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!token) return

    if (saleForm.items.length === 0) {
      toast.error('Please add at least one item')
      return
    }

    try {
      setSubmitting(true)

      const response = await fetch('/api/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify(saleForm)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create sale')
      }

      toast.success('Sale created successfully')
      setShowDialog(false)
      fetchSales(pagination.page, pagination.limit, search)
      fetchStats()
    } catch (error: any) {
      toast.error(error.message)
    } finally {
      setSubmitting(false)
    }
  }

  useEffect(() => {
    fetchSales()
    fetchProducts()
    fetchCustomers()
    fetchStats()
  }, [token])

  return (
    <div className="p-6 space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Sales</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.todaySales}</div>
            <p className="text-xs text-muted-foreground">
              ₹{stats.todayAmount.toFixed(2)} revenue
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Month's Sales</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.monthSales}</div>
            <p className="text-xs text-muted-foreground">
              ₹{stats.monthAmount.toFixed(2)} revenue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Sale Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ₹{stats.monthSales > 0 ? (stats.monthAmount / stats.monthSales).toFixed(2) : '0.00'}
            </div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Growth</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+12.5%</div>
            <p className="text-xs text-muted-foreground">vs last month</p>
          </CardContent>
        </Card>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Sales</h1>
          <p className="text-muted-foreground">Manage your sales transactions</p>
        </div>
      </div>

      {/* Sales Table */}
      <DataTable
        columns={columns}
        data={sales}
        loading={loading}
        pagination={pagination}
        onPageChange={(page) => fetchSales(page, pagination.limit, search)}
        onLimitChange={(limit) => fetchSales(1, limit, search)}
        onSearch={(searchTerm) => {
          setSearch(searchTerm)
          fetchSales(1, pagination.limit, searchTerm)
        }}
        onAdd={handleAdd}
        searchPlaceholder="Search sales..."
        title=""
        addButtonText="New Sale"
      />

      {/* Create Sale Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Sale</DialogTitle>
            <DialogDescription>
              Add products and customer details to create a new sale.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Customer Details */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="customerId">Customer</Label>
                <Select 
                  value={saleForm.customerId} 
                  onValueChange={(value) => setSaleForm(prev => ({ ...prev, customerId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select customer (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.map((customer) => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.name} {customer.phone && `(${customer.phone})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select 
                  value={saleForm.paymentMethod} 
                  onValueChange={(value: any) => setSaleForm(prev => ({ ...prev, paymentMethod: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CASH">Cash</SelectItem>
                    <SelectItem value="CARD">Card</SelectItem>
                    <SelectItem value="UPI">UPI</SelectItem>
                    <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                    <SelectItem value="CREDIT">Credit</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Walk-in Customer Details */}
            {!saleForm.customerId && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="customerName">Customer Name (Optional)</Label>
                  <Input
                    id="customerName"
                    value={saleForm.customerName}
                    onChange={(e) => setSaleForm(prev => ({ ...prev, customerName: e.target.value }))}
                    placeholder="Enter customer name"
                  />
                </div>
                <div>
                  <Label htmlFor="customerPhone">Phone (Optional)</Label>
                  <Input
                    id="customerPhone"
                    value={saleForm.customerPhone}
                    onChange={(e) => setSaleForm(prev => ({ ...prev, customerPhone: e.target.value }))}
                    placeholder="Enter phone number"
                  />
                </div>
              </div>
            )}

            {/* Items */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <Label>Items</Label>
                <Button type="button" onClick={addItem} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>
              
              {saleForm.items.map((item, index) => (
                <div key={index} className="grid grid-cols-6 gap-2 mb-2 p-3 border rounded">
                  <div>
                    <Select 
                      value={item.productId} 
                      onValueChange={(value) => updateItem(index, 'productId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Product" />
                      </SelectTrigger>
                      <SelectContent>
                        {products.map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            {product.name} ({product.sku})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Input
                      type="number"
                      min="1"
                      value={item.quantity}
                      onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                      placeholder="Qty"
                    />
                  </div>
                  <div>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={item.unitPrice}
                      onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                      placeholder="Price"
                    />
                  </div>
                  <div>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={item.discount}
                      onChange={(e) => updateItem(index, 'discount', parseFloat(e.target.value) || 0)}
                      placeholder="Discount"
                    />
                  </div>
                  <div>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      value={item.taxRate}
                      onChange={(e) => updateItem(index, 'taxRate', parseFloat(e.target.value) || 0)}
                      placeholder="Tax %"
                    />
                  </div>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeItem(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Discount and Notes */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="discount">Overall Discount</Label>
                <Input
                  id="discount"
                  type="number"
                  step="0.01"
                  min="0"
                  value={saleForm.discount}
                  onChange={(e) => setSaleForm(prev => ({ ...prev, discount: parseFloat(e.target.value) || 0 }))}
                  placeholder="0.00"
                />
              </div>
              <div>
                <Label>Total Amount</Label>
                <div className="text-2xl font-bold text-green-600">
                  ₹{calculateTotal().toFixed(2)}
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={saleForm.notes}
                onChange={(e) => setSaleForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Enter any notes"
                rows={2}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowDialog(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? 'Creating...' : 'Create Sale'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* View Sale Dialog */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Sale Details</DialogTitle>
            <DialogDescription>
              {selectedSale?.saleNo} - {new Date(selectedSale?.createdAt || '').toLocaleDateString()}
            </DialogDescription>
          </DialogHeader>
          {selectedSale && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Customer</Label>
                  <div className="font-medium">
                    {selectedSale.customer?.name || 'Walk-in Customer'}
                  </div>
                  {selectedSale.customer?.phone && (
                    <div className="text-sm text-muted-foreground">
                      {selectedSale.customer.phone}
                    </div>
                  )}
                </div>
                <div>
                  <Label>Payment Method</Label>
                  <div className="font-medium">{selectedSale.paymentMethod}</div>
                </div>
              </div>

              <div>
                <Label>Items</Label>
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-muted">
                      <tr>
                        <th className="text-left p-2">Product</th>
                        <th className="text-right p-2">Qty</th>
                        <th className="text-right p-2">Price</th>
                        <th className="text-right p-2">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedSale.items.map((item) => (
                        <tr key={item.id} className="border-t">
                          <td className="p-2">
                            <div className="font-medium">{item.product.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {item.product.sku}
                            </div>
                          </td>
                          <td className="text-right p-2">
                            {item.quantity} {item.product.unit}
                          </td>
                          <td className="text-right p-2">
                            ₹{item.unitPrice.toFixed(2)}
                          </td>
                          <td className="text-right p-2">
                            ₹{item.totalPrice.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <span>Subtotal:</span>
                  <span>₹{(selectedSale.totalAmount + selectedSale.discount - selectedSale.taxAmount).toFixed(2)}</span>
                </div>
                {selectedSale.discount > 0 && (
                  <div className="flex justify-between items-center">
                    <span>Discount:</span>
                    <span>-₹{selectedSale.discount.toFixed(2)}</span>
                  </div>
                )}
                {selectedSale.taxAmount > 0 && (
                  <div className="flex justify-between items-center">
                    <span>Tax:</span>
                    <span>₹{selectedSale.taxAmount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between items-center font-bold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>₹{selectedSale.totalAmount.toFixed(2)}</span>
                </div>
              </div>

              {selectedSale.notes && (
                <div>
                  <Label>Notes</Label>
                  <div className="text-sm">{selectedSale.notes}</div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
