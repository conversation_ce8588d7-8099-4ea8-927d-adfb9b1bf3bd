import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/stores - Get all stores with advanced filtering
export const GET = withPermission('STORE', 'READ', async (request: NextRequest, user: any) => {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '10')
  const search = searchParams.get('search') || ''
  const status = searchParams.get('status') || 'all'
  const sortBy = searchParams.get('sortBy') || 'createdAt'
  const sortOrder = searchParams.get('sortOrder') || 'desc'
  const region = searchParams.get('region') || ''
  const storeType = searchParams.get('storeType') || ''

  const skip = (page - 1) * limit

  const where: any = {}

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { address: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { gstNumber: { contains: search, mode: 'insensitive' } }
    ]
  }

  if (status !== 'all') {
    where.isActive = status === 'active'
  }

  if (region) {
    where.region = region
  }

  if (storeType) {
    where.storeType = storeType
  }

  // For non-founder users, only show their store
  if (user.role !== 'FOUNDER' && user.role !== 'SUPER_ADMIN') {
    where.id = user.storeId
  }

  const [stores, total] = await Promise.all([
    prisma.store.findMany({
      where,
      skip,
      take: limit,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        _count: {
          select: {
            users: true,
            products: true,
            sales: true,
            customers: true,
            suppliers: true
          }
        }
      },
      orderBy: { [sortBy]: sortOrder }
    }),
    prisma.store.count({ where })
  ])

  // Calculate additional metrics
  const storesWithMetrics = await Promise.all(
    stores.map(async (store) => {
      const [todaySales, monthSales, lowStockCount] = await Promise.all([
        prisma.sale.aggregate({
          where: {
            storeId: store.id,
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          },
          _sum: { total: true }
        }),
        prisma.sale.aggregate({
          where: {
            storeId: store.id,
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          },
          _sum: { total: true }
        }),
        prisma.inventory.count({
          where: {
            storeId: store.id,
            quantity: { lte: prisma.inventory.fields.reorderLevel }
          }
        })
      ])

      return {
        ...store,
        metrics: {
          todaySales: todaySales._sum.total || 0,
          monthSales: monthSales._sum.total || 0,
          lowStockCount
        }
      }
    })
  )

  return createSuccessResponse({
    stores: storesWithMetrics,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  })
})

// POST /api/stores - Create new store
export const POST = withPermission('STORE', 'CREATE', async (request: NextRequest, user: any) => {
  const body = await request.json()

  const schema = z.object({
    name: z.string().min(1, 'Store name is required'),
    address: z.string().min(1, 'Address is required'),
    phone: z.string().min(1, 'Phone is required'),
    email: z.string().email('Valid email is required'),
    gstNumber: z.string().optional(),
    region: z.string().optional(),
    storeType: z.enum(['RETAIL', 'WHOLESALE', 'HYBRID']).default('RETAIL'),
    managerName: z.string().optional(),
    managerPhone: z.string().optional(),
    operatingHours: z.object({
      monday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      tuesday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      wednesday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      thursday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      friday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      saturday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      sunday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) })
    }).optional(),
    settings: z.object({
      currency: z.string().default('INR'),
      taxEnabled: z.boolean().default(true),
      loyaltyEnabled: z.boolean().default(false),
      discountEnabled: z.boolean().default(true),
      barcodeEnabled: z.boolean().default(true)
    }).optional()
  })

  const validatedData = schema.parse(body)

  // Check if store with same name already exists
  const existingStore = await prisma.store.findFirst({
    where: { name: validatedData.name }
  })

  if (existingStore) {
    return createErrorResponse('Store with this name already exists', 400)
  }

  const store = await prisma.store.create({
    data: {
      ...validatedData,
      createdById: user.id,
      isActive: true
    },
    include: {
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'STORE',
    `Created store: ${store.name}`,
    user.id,
    store.id
  )

  return createSuccessResponse(store, 'Store created successfully')
})
