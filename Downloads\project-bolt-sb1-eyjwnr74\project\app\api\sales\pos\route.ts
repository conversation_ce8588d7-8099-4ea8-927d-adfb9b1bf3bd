import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// POST /api/sales/pos - Create POS sale
export const POST = withPermission('SALES', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  const posSchema = z.object({
    customerId: z.string().optional(),
    customerName: z.string().optional(),
    customerPhone: z.string().optional(),
    items: z.array(z.object({
      productId: z.string().min(1, 'Product is required'),
      variantId: z.string().optional(),
      quantity: z.number().min(1, 'Quantity must be at least 1'),
      unitPrice: z.number().min(0, 'Unit price must be positive'),
      discount: z.number().min(0).default(0),
      taxRate: z.number().min(0).max(100).default(0)
    })).min(1, 'At least one item is required'),
    payments: z.array(z.object({
      method: z.enum(['CASH', 'CARD', 'UPI', 'BANK_TRANSFER', 'CREDIT']),
      amount: z.number().min(0, 'Payment amount must be positive'),
      reference: z.string().optional()
    })).min(1, 'At least one payment method is required'),
    discount: z.number().min(0).default(0),
    discountType: z.enum(['AMOUNT', 'PERCENTAGE']).default('AMOUNT'),
    taxAmount: z.number().min(0).default(0),
    notes: z.string().optional(),
    printReceipt: z.boolean().default(true)
  })

  const saleData = posSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate products and check inventory
      const productIds = saleData.items.map(item => item.productId)
      const products = await tx.product.findMany({
        where: {
          id: { in: productIds },
          storeId,
          isActive: true
        },
        include: {
          inventory: {
            where: { storeId }
          }
        }
      })

      if (products.length !== productIds.length) {
        throw new Error('One or more products not found')
      }

      // Check inventory availability
      for (const item of saleData.items) {
        const product = products.find(p => p.id === item.productId)
        if (!product) continue

        const inventory = product.inventory[0]
        if (!inventory || inventory.quantity < item.quantity) {
          throw new Error(`Insufficient stock for product: ${product.name}. Available: ${inventory?.quantity || 0}, Required: ${item.quantity}`)
        }
      }

      // Handle customer
      let customerId = saleData.customerId
      if (!customerId && (saleData.customerName || saleData.customerPhone)) {
        // Create or find customer
        if (saleData.customerPhone) {
          const existingCustomer = await tx.customer.findFirst({
            where: {
              phone: saleData.customerPhone,
              storeId
            }
          })

          if (existingCustomer) {
            customerId = existingCustomer.id
          } else {
            const newCustomer = await tx.customer.create({
              data: {
                name: saleData.customerName || 'Walk-in Customer',
                phone: saleData.customerPhone,
                storeId
              }
            })
            customerId = newCustomer.id
          }
        }
      }

      // Generate sale number
      const saleCount = await tx.sale.count({ where: { storeId } })
      const saleNo = `SAL-${String(saleCount + 1).padStart(6, '0')}`

      // Calculate totals
      let subtotal = 0
      let totalTax = 0
      let totalItemDiscount = 0

      const saleItems = saleData.items.map(item => {
        const itemSubtotal = item.quantity * item.unitPrice
        const itemDiscount = item.discount || 0
        const itemTaxableAmount = itemSubtotal - itemDiscount
        const itemTax = (itemTaxableAmount * item.taxRate) / 100

        subtotal += itemSubtotal
        totalItemDiscount += itemDiscount
        totalTax += itemTax

        return {
          productId: item.productId,
          variantId: item.variantId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: itemDiscount,
          taxRate: item.taxRate,
          taxAmount: itemTax,
          totalPrice: itemTaxableAmount + itemTax
        }
      })

      // Apply overall discount
      let overallDiscount = saleData.discount || 0
      if (saleData.discountType === 'PERCENTAGE') {
        overallDiscount = (subtotal * overallDiscount) / 100
      }

      const totalDiscount = totalItemDiscount + overallDiscount
      const finalTotal = subtotal - totalDiscount + totalTax

      // Validate payment amount
      const totalPayment = saleData.payments.reduce((sum, payment) => sum + payment.amount, 0)
      if (Math.abs(totalPayment - finalTotal) > 0.01) {
        throw new Error(`Payment amount (₹${totalPayment}) does not match total amount (₹${finalTotal})`)
      }

      // Create sale
      const sale = await tx.sale.create({
        data: {
          saleNo,
          saleDate: new Date(),
          customerId,
          subtotal,
          discount: totalDiscount,
          taxAmount: totalTax,
          total: finalTotal,
          status: 'COMPLETED',
          paymentStatus: 'PAID',
          notes: saleData.notes,
          createdById: user.id,
          storeId,
          items: {
            create: saleItems
          },
          payments: {
            create: saleData.payments.map(payment => ({
              method: payment.method,
              amount: payment.amount,
              reference: payment.reference,
              storeId
            }))
          }
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  unit: true,
                  barcode: true
                }
              },
              variant: {
                select: {
                  id: true,
                  name: true,
                  sku: true
                }
              }
            }
          },
          payments: true,
          customer: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true
            }
          },
          createdBy: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Update inventory
      for (const item of saleData.items) {
        const inventory = await tx.inventory.findFirst({
          where: {
            productId: item.productId,
            ...(item.variantId && { variantId: item.variantId }),
            storeId
          }
        })

        if (inventory) {
          await tx.inventory.update({
            where: { id: inventory.id },
            data: {
              quantity: inventory.quantity - item.quantity,
              lastUpdated: new Date()
            }
          })

          // Create inventory adjustment record
          await tx.inventoryAdjustment.create({
            data: {
              productId: item.productId,
              variantId: item.variantId,
              storeId,
              adjustmentType: 'OUT',
              quantityChanged: -item.quantity,
              reason: `Sale: ${saleNo}`,
              createdById: user.id
            }
          })
        }
      }

      // Update customer purchase history
      if (customerId) {
        await tx.customer.update({
          where: { id: customerId },
          data: {
            totalPurchases: { increment: finalTotal },
            lastPurchaseDate: new Date()
          }
        })
      }

      return sale
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'SALES',
      `POS Sale: ${result.saleNo} - Total: ₹${result.total}${result.customer ? ` - Customer: ${result.customer.name}` : ''}`,
      user.id,
      storeId
    )

    return createSuccessResponse({
      sale: result,
      receipt: saleData.printReceipt ? generateReceipt(result) : null
    }, 'Sale completed successfully')

  } catch (error) {
    console.error('Error creating POS sale:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create sale',
      400
    )
  }
})

// Helper function to generate receipt data
function generateReceipt(sale: any) {
  return {
    saleNo: sale.saleNo,
    date: sale.saleDate,
    customer: sale.customer,
    items: sale.items.map((item: any) => ({
      name: item.product.name,
      variant: item.variant?.name,
      sku: item.variant?.sku || item.product.sku,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      discount: item.discount,
      taxRate: item.taxRate,
      total: item.totalPrice
    })),
    subtotal: sale.subtotal,
    discount: sale.discount,
    taxAmount: sale.taxAmount,
    total: sale.total,
    payments: sale.payments,
    cashier: sale.createdBy.name,
    timestamp: new Date().toISOString()
  }
}
