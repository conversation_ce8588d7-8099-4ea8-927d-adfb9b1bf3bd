generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model User {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  email       String    @unique
  password    String
  name        String
  phone       String?
  role        Role      @default(STAFF)
  isActive    Boolean   @default(true)
  permissions String[]  @default([])
  lastLoginAt DateTime?
  storeId     String?   @db.ObjectId
  store       Store?    @relation(fields: [storeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  createdStores        Store[]               @relation("StoreCreatedBy")
  purchases            Purchase[]
  sales                Sale[]
  expenses             Expense[]
  auditLogs            AuditLog[]
  supportTickets       SupportTicket[]
  notifications        Notification[]        @relation("NotificationUser")
  documents            Document[]
  purchaseReturns      PurchaseReturn[]
  salesReturns         SaleReturn[]
  inventoryAdjustments InventoryAdjustment[]

  @@map("users")
}

model Store {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  address     String
  phone       String
  email       String?
  gstNumber   String?
  isActive    Boolean  @default(true)
  createdById String   @db.ObjectId
  createdBy   User     @relation("StoreCreatedBy", fields: [createdById], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users                User[]
  products             Product[]
  inventory            Inventory[]
  inventoryAdjustments InventoryAdjustment[]
  purchases            Purchase[]
  sales                Sale[]
  customers            Customer[]
  suppliers            Supplier[]
  distributors         Distributor[]
  expenses             Expense[]
  categories           Category[]
  subscriptions        Subscription[]
  documents            Document[]
  notifications        Notification[]
  auditLogs            AuditLog[]
  supportTickets       SupportTicket[]
  b2bOrders            B2BOrder[]

  @@map("stores")
}

model Category {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  isActive    Boolean  @default(true)
  storeId     String   @db.ObjectId
  store       Store    @relation(fields: [storeId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  products Product[]

  @@map("categories")
}

model Product {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  name         String
  sku          String   @unique
  description  String?
  barcode      String?
  unit         String   @default("pcs")
  costPrice    Float
  sellingPrice Float
  mrp          Float?
  taxRate      Float    @default(0)
  minStock     Int      @default(0)
  isActive     Boolean  @default(true)
  categoryId   String   @db.ObjectId
  category     Category @relation(fields: [categoryId], references: [id])
  storeId      String   @db.ObjectId
  store        Store    @relation(fields: [storeId], references: [id])
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  inventory            Inventory[]
  inventoryAdjustments InventoryAdjustment[]
  purchaseItems        PurchaseItem[]
  saleItems            SaleItem[]
  b2bOrderItems        B2BOrderItem[]
  returnItems          ReturnItem[]
  variants             ProductVariant[]

  @@map("products")
}

model ProductVariant {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  name         String
  sku          String   @unique
  barcode      String?
  costPrice    Float
  sellingPrice Float
  mrp          Float?
  attributes   Json? // Store variant attributes like size, color, etc.
  isActive     Boolean  @default(true)
  productId    String   @db.ObjectId
  product      Product  @relation(fields: [productId], references: [id])
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  inventory Inventory[]

  @@map("product_variants")
}

model Inventory {
  id           String          @id @default(auto()) @map("_id") @db.ObjectId
  productId    String          @db.ObjectId
  product      Product         @relation(fields: [productId], references: [id])
  storeId      String          @db.ObjectId
  store        Store           @relation(fields: [storeId], references: [id])
  quantity     Int             @default(0)
  reorderLevel Int             @default(0)
  lastUpdated  DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  variant      ProductVariant? @relation(fields: [variantId], references: [id])
  variantId    String?         @db.ObjectId

  @@unique([productId, variantId, storeId])
  @@map("inventory")
}

model InventoryAdjustment {
  id              String         @id @default(auto()) @map("_id") @db.ObjectId
  productId       String         @db.ObjectId
  product         Product        @relation(fields: [productId], references: [id])
  storeId         String         @db.ObjectId
  store           Store          @relation(fields: [storeId], references: [id])
  adjustmentType  AdjustmentType
  quantityBefore  Int
  quantityAfter   Int
  quantityChanged Int
  reason          String
  notes           String?
  createdById     String         @db.ObjectId
  createdBy       User           @relation(fields: [createdById], references: [id])
  createdAt       DateTime       @default(now())

  @@map("inventory_adjustments")
}

model Supplier {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  name      String
  email     String?
  phone     String
  address   String
  gstNumber String?
  isActive  Boolean  @default(true)
  storeId   String   @db.ObjectId
  store     Store    @relation(fields: [storeId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  purchases Purchase[]

  @@map("suppliers")
}

model Distributor {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  name      String
  email     String?
  phone     String
  address   String
  gstNumber String?
  isActive  Boolean  @default(true)
  storeId   String   @db.ObjectId
  store     Store    @relation(fields: [storeId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  purchases Purchase[]

  @@map("distributors")
}

model Purchase {
  id                   String         @id @default(auto()) @map("_id") @db.ObjectId
  purchaseNo           String         @unique
  purchaseDate         DateTime       @default(now())
  expectedDeliveryDate DateTime?
  supplierId           String?        @db.ObjectId
  supplier             Supplier?      @relation(fields: [supplierId], references: [id])
  distributorId        String?        @db.ObjectId
  distributor          Distributor?   @relation(fields: [distributorId], references: [id])
  subtotal             Float          @default(0)
  discount             Float          @default(0)
  taxAmount            Float          @default(0)
  totalAmount          Float
  paidAmount           Float          @default(0)
  status               PurchaseStatus @default(PENDING)
  notes                String?
  storeId              String         @db.ObjectId
  store                Store          @relation(fields: [storeId], references: [id])
  createdById          String         @db.ObjectId
  createdBy            User           @relation(fields: [createdById], references: [id])
  createdAt            DateTime       @default(now())
  updatedAt            DateTime       @updatedAt

  // Relations
  items    PurchaseItem[]
  returns  PurchaseReturn[]
  payments Payment[]

  @@map("purchases")
}

model PurchaseItem {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  purchaseId String   @db.ObjectId
  purchase   Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)
  productId  String   @db.ObjectId
  product    Product  @relation(fields: [productId], references: [id])
  quantity   Int
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime @default(now())

  @@map("purchase_items")
}

model PurchaseReturn {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  returnNo    String   @unique
  purchaseId  String   @db.ObjectId
  purchase    Purchase @relation(fields: [purchaseId], references: [id])
  totalAmount Float
  reason      String
  storeId     String   @db.ObjectId
  createdById String   @db.ObjectId
  createdBy   User     @relation(fields: [createdById], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  items ReturnItem[]

  @@map("purchase_returns")
}

model Customer {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  name          String
  email         String?
  phone         String?
  address       String?
  loyaltyPoints Int      @default(0)
  isActive      Boolean  @default(true)
  storeId       String   @db.ObjectId
  store         Store    @relation(fields: [storeId], references: [id])
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  sales     Sale[]
  b2bOrders B2BOrder[]
  feedback  Feedback[]

  @@map("customers")
}

model Sale {
  id            String        @id @default(auto()) @map("_id") @db.ObjectId
  saleNo        String        @unique
  customerId    String?       @db.ObjectId
  customer      Customer?     @relation(fields: [customerId], references: [id])
  totalAmount   Float
  taxAmount     Float         @default(0)
  discount      Float         @default(0)
  paidAmount    Float
  paymentMethod PaymentMethod @default(CASH)
  status        SaleStatus    @default(COMPLETED)
  notes         String?
  storeId       String        @db.ObjectId
  store         Store         @relation(fields: [storeId], references: [id])
  createdById   String        @db.ObjectId
  createdBy     User          @relation(fields: [createdById], references: [id])
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  items    SaleItem[]
  returns  SaleReturn[]
  payments Payment[]

  @@map("sales")
}

model SaleItem {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  saleId     String   @db.ObjectId
  sale       Sale     @relation(fields: [saleId], references: [id], onDelete: Cascade)
  productId  String   @db.ObjectId
  product    Product  @relation(fields: [productId], references: [id])
  quantity   Int
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime @default(now())

  @@map("sale_items")
}

model SaleReturn {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  returnNo    String   @unique
  saleId      String   @db.ObjectId
  sale        Sale     @relation(fields: [saleId], references: [id])
  totalAmount Float
  reason      String
  storeId     String   @db.ObjectId
  createdById String   @db.ObjectId
  createdBy   User     @relation(fields: [createdById], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  items ReturnItem[]

  @@map("sale_returns")
}

model ReturnItem {
  id               String          @id @default(auto()) @map("_id") @db.ObjectId
  productId        String          @db.ObjectId
  product          Product         @relation(fields: [productId], references: [id])
  quantity         Int
  unitPrice        Float
  totalPrice       Float
  purchaseReturnId String?         @db.ObjectId
  purchaseReturn   PurchaseReturn? @relation(fields: [purchaseReturnId], references: [id])
  saleReturnId     String?         @db.ObjectId
  saleReturn       SaleReturn?     @relation(fields: [saleReturnId], references: [id])
  createdAt        DateTime        @default(now())

  @@map("return_items")
}

model B2BOrder {
  id          String         @id @default(auto()) @map("_id") @db.ObjectId
  orderNo     String         @unique
  customerId  String         @db.ObjectId
  customer    Customer       @relation(fields: [customerId], references: [id])
  totalAmount Float
  status      B2BOrderStatus @default(PENDING)
  notes       String?
  storeId     String         @db.ObjectId
  store       Store          @relation(fields: [storeId], references: [id])
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  items B2BOrderItem[]

  @@map("b2b_orders")
}

model B2BOrderItem {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  orderId    String   @db.ObjectId
  order      B2BOrder @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId  String   @db.ObjectId
  product    Product  @relation(fields: [productId], references: [id])
  quantity   Int
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime @default(now())

  @@map("b2b_order_items")
}

model Expense {
  id          String          @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  amount      Float
  category    ExpenseCategory
  date        DateTime
  receiptUrl  String?
  storeId     String          @db.ObjectId
  store       Store           @relation(fields: [storeId], references: [id])
  createdById String          @db.ObjectId
  createdBy   User            @relation(fields: [createdById], references: [id])
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  @@map("expenses")
}

model Payment {
  id         String        @id @default(auto()) @map("_id") @db.ObjectId
  amount     Float
  method     PaymentMethod
  status     PaymentStatus @default(PENDING)
  reference  String?
  notes      String?
  purchaseId String?       @db.ObjectId
  purchase   Purchase?     @relation(fields: [purchaseId], references: [id])
  saleId     String?       @db.ObjectId
  sale       Sale?         @relation(fields: [saleId], references: [id])
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  @@map("payments")
}

model Notification {
  id        String           @id @default(auto()) @map("_id") @db.ObjectId
  title     String
  message   String
  type      NotificationType @default(INFO)
  isRead    Boolean          @default(false)
  userId    String           @db.ObjectId
  user      User             @relation("NotificationUser", fields: [userId], references: [id])
  storeId   String           @db.ObjectId
  store     Store            @relation(fields: [storeId], references: [id])
  createdAt DateTime         @default(now())

  @@map("notifications")
}

model Document {
  id           String       @id @default(auto()) @map("_id") @db.ObjectId
  title        String
  fileName     String
  fileUrl      String
  fileSize     Int
  mimeType     String
  category     DocumentType @default(OTHER)
  storeId      String       @db.ObjectId
  store        Store        @relation(fields: [storeId], references: [id])
  uploadedById String       @db.ObjectId
  uploadedBy   User         @relation(fields: [uploadedById], references: [id])
  createdAt    DateTime     @default(now())

  @@map("documents")
}

model Subscription {
  id        String             @id @default(auto()) @map("_id") @db.ObjectId
  planName  String
  features  String[]
  price     Float
  status    SubscriptionStatus @default(ACTIVE)
  startDate DateTime
  endDate   DateTime
  storeId   String             @db.ObjectId
  store     Store              @relation(fields: [storeId], references: [id])
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt

  @@map("subscriptions")
}

model Feedback {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  rating     Int // 1-5
  comment    String?
  customerId String   @db.ObjectId
  customer   Customer @relation(fields: [customerId], references: [id])
  createdAt  DateTime @default(now())

  @@map("feedback")
}

model SupportTicket {
  id          String         @id @default(auto()) @map("_id") @db.ObjectId
  ticketNo    String         @unique
  subject     String
  description String
  priority    TicketPriority @default(MEDIUM)
  status      TicketStatus   @default(OPEN)
  storeId     String         @db.ObjectId
  store       Store          @relation(fields: [storeId], references: [id])
  createdById String         @db.ObjectId
  createdBy   User           @relation(fields: [createdById], references: [id])
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  @@map("support_tickets")
}

model AuditLog {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  action    String
  module    String
  details   String?
  userId    String   @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])
  storeId   String   @db.ObjectId
  store     Store    @relation(fields: [storeId], references: [id])
  createdAt DateTime @default(now())

  @@map("audit_logs")
}

// Enums
enum Role {
  FOUNDER
  SUPER_ADMIN
  ADMIN
  STAFF
  DISTRIBUTOR
}

enum PurchaseStatus {
  PENDING
  APPROVED
  COMPLETED
  CANCELLED
}

enum SaleStatus {
  PENDING
  COMPLETED
  CANCELLED
  RETURNED
}

enum B2BOrderStatus {
  PENDING
  APPROVED
  PROCESSING
  COMPLETED
  CANCELLED
}

enum PaymentMethod {
  CASH
  CARD
  UPI
  BANK_TRANSFER
  CREDIT
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum ExpenseCategory {
  RENT
  UTILITIES
  SALARY
  MARKETING
  SUPPLIES
  MAINTENANCE
  OTHER
}

enum NotificationType {
  INFO
  WARNING
  ERROR
  SUCCESS
}

enum DocumentType {
  INVOICE
  RECEIPT
  CONTRACT
  CERTIFICATE
  OTHER
}

enum SubscriptionStatus {
  ACTIVE
  EXPIRED
  CANCELLED
  SUSPENDED
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum AdjustmentType {
  IN
  OUT
  SET
}
