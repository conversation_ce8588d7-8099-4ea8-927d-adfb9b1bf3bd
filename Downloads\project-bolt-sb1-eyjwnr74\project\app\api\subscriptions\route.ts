import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const subscriptionSchema = z.object({
  planId: z.string().min(1, 'Plan ID is required'),
  billingCycle: z.enum(['MONTHLY', 'QUARTERLY', 'YEARLY']).default('MONTHLY'),
  startDate: z.string().optional(),
  trialDays: z.number().min(0).max(365).default(0),
  customPricing: z.object({
    amount: z.number().min(0),
    currency: z.string().default('INR')
  }).optional(),
  addons: z.array(z.object({
    addonId: z.string(),
    quantity: z.number().min(1).default(1)
  })).optional(),
  metadata: z.record(z.any()).optional()
})

const planSchema = z.object({
  name: z.string().min(1, 'Plan name is required'),
  description: z.string().optional(),
  category: z.enum(['BASIC', 'PROFESSIONAL', 'ENTERPRISE', 'CUSTOM']),
  pricing: z.object({
    monthly: z.number().min(0),
    quarterly: z.number().min(0).optional(),
    yearly: z.number().min(0).optional(),
    currency: z.string().default('INR')
  }),
  features: z.array(z.object({
    name: z.string(),
    description: z.string().optional(),
    included: z.boolean(),
    limit: z.number().optional(),
    unit: z.string().optional()
  })),
  limits: z.object({
    users: z.number().min(1),
    stores: z.number().min(1),
    products: z.number().min(1),
    transactions: z.number().min(1),
    storage: z.number().min(1), // in GB
    apiCalls: z.number().min(1)
  }),
  isActive: z.boolean().default(true),
  isPublic: z.boolean().default(true),
  trialDays: z.number().min(0).max(365).default(14)
})

// GET /api/subscriptions - Get subscriptions with filtering
export const GET = withPermission('SUBSCRIPTION', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const status = searchParams.get('status')
  const planId = searchParams.get('planId')
  const billingCycle = searchParams.get('billingCycle')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const includeUsage = searchParams.get('includeUsage') === 'true'

  try {
    // Build filters
    const where: any = {
      storeId,
      ...buildSearchFilter(search, ['plan.name']),
      ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
    }

    if (status) {
      where.status = status
    }

    if (planId) {
      where.planId = planId
    }

    if (billingCycle) {
      where.billingCycle = billingCycle
    }

    // Get total count
    const total = await prisma.subscription.count({ where })

    // Get subscriptions with pagination
    const subscriptions = await prisma.subscription.findMany({
      where,
      include: {
        plan: {
          select: {
            id: true,
            name: true,
            category: true,
            pricing: true,
            features: true,
            limits: true
          }
        },
        addons: {
          include: {
            addon: {
              select: {
                id: true,
                name: true,
                pricing: true,
                description: true
              }
            }
          }
        },
        invoices: {
          select: {
            id: true,
            amount: true,
            status: true,
            dueDate: true,
            paidAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 5
        },
        _count: {
          select: {
            invoices: true
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    })

    // Add calculated fields
    const subscriptionsWithCalculations = await Promise.all(
      subscriptions.map(async (subscription) => {
        const calculations = {
          ...subscription,
          daysRemaining: calculateDaysRemaining(subscription),
          isTrialActive: isTrialActive(subscription),
          nextBillingDate: calculateNextBillingDate(subscription),
          totalMonthlyValue: calculateTotalMonthlyValue(subscription),
          usagePercentage: includeUsage ? await calculateUsagePercentage(subscription) : null,
          renewalStatus: determineRenewalStatus(subscription)
        }

        return calculations
      })
    )

    return createPaginatedResponse(subscriptionsWithCalculations, page, limit, total)

  } catch (error) {
    console.error('Error fetching subscriptions:', error)
    return createErrorResponse('Failed to fetch subscriptions', 500)
  }
})

// POST /api/subscriptions - Create new subscription
export const POST = withPermission('SUBSCRIPTION', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = subscriptionSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Validate plan exists and is active
      const plan = await tx.subscriptionPlan.findFirst({
        where: {
          id: data.planId,
          isActive: true
        },
        include: {
          features: true,
          limits: true
        }
      })

      if (!plan) {
        throw new Error('Subscription plan not found or inactive')
      }

      // Check if store already has an active subscription
      const existingSubscription = await tx.subscription.findFirst({
        where: {
          storeId,
          status: { in: ['ACTIVE', 'TRIAL'] }
        }
      })

      if (existingSubscription) {
        throw new Error('Store already has an active subscription')
      }

      // Validate addons if provided
      let validatedAddons = []
      if (data.addons && data.addons.length > 0) {
        const addonIds = data.addons.map(a => a.addonId)
        const addons = await tx.subscriptionAddon.findMany({
          where: {
            id: { in: addonIds },
            isActive: true
          }
        })

        if (addons.length !== addonIds.length) {
          throw new Error('One or more addons not found or inactive')
        }

        validatedAddons = data.addons.map(addon => {
          const addonDetails = addons.find(a => a.id === addon.addonId)!
          return {
            addonId: addon.addonId,
            quantity: addon.quantity,
            unitPrice: addonDetails.pricing.monthly,
            totalPrice: addonDetails.pricing.monthly * addon.quantity
          }
        })
      }

      // Calculate pricing
      const startDate = data.startDate ? new Date(data.startDate) : new Date()
      const trialEndDate = data.trialDays > 0 ? 
        new Date(startDate.getTime() + data.trialDays * 24 * 60 * 60 * 1000) : null

      const basePrice = data.customPricing?.amount ?? plan.pricing[data.billingCycle.toLowerCase()]
      const addonsPrice = validatedAddons.reduce((sum, addon) => sum + addon.totalPrice, 0)
      const totalPrice = basePrice + addonsPrice

      // Create subscription
      const subscription = await tx.subscription.create({
        data: {
          storeId,
          planId: data.planId,
          status: data.trialDays > 0 ? 'TRIAL' : 'ACTIVE',
          billingCycle: data.billingCycle,
          startDate,
          trialEndDate,
          currentPeriodStart: trialEndDate || startDate,
          currentPeriodEnd: calculatePeriodEnd(trialEndDate || startDate, data.billingCycle),
          basePrice,
          totalPrice,
          currency: data.customPricing?.currency || plan.pricing.currency,
          metadata: data.metadata,
          createdById: user.id
        },
        include: {
          plan: true
        }
      })

      // Create addon subscriptions
      if (validatedAddons.length > 0) {
        await tx.subscriptionAddonUsage.createMany({
          data: validatedAddons.map(addon => ({
            subscriptionId: subscription.id,
            addonId: addon.addonId,
            quantity: addon.quantity,
            unitPrice: addon.unitPrice,
            totalPrice: addon.totalPrice,
            storeId
          }))
        })
      }

      // Create first invoice if not in trial
      if (data.trialDays === 0) {
        await createSubscriptionInvoice(subscription, tx)
      }

      // Apply plan limits to store
      await applyPlanLimits(storeId, plan.limits, tx)

      return subscription
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'SUBSCRIPTION',
      `Created subscription: ${result.plan.name} (${result.billingCycle})`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Subscription created successfully')

  } catch (error) {
    console.error('Error creating subscription:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create subscription',
      400
    )
  }
})

// PUT /api/subscriptions/[id] - Update subscription
export const PUT = withPermission('SUBSCRIPTION', 'UPDATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const subscriptionId = url.pathname.split('/').pop()
  const body = await request.json()

  const updateSchema = z.object({
    planId: z.string().optional(),
    billingCycle: z.enum(['MONTHLY', 'QUARTERLY', 'YEARLY']).optional(),
    addons: z.array(z.object({
      addonId: z.string(),
      quantity: z.number().min(1)
    })).optional(),
    metadata: z.record(z.any()).optional()
  })

  const data = updateSchema.parse(body)

  try {
    const result = await prisma.$transaction(async (tx) => {
      // Get current subscription
      const subscription = await tx.subscription.findFirst({
        where: {
          id: subscriptionId,
          storeId
        },
        include: {
          plan: true,
          addons: true
        }
      })

      if (!subscription) {
        throw new Error('Subscription not found')
      }

      if (subscription.status === 'CANCELLED') {
        throw new Error('Cannot update cancelled subscription')
      }

      let updateData: any = {}

      // Handle plan change
      if (data.planId && data.planId !== subscription.planId) {
        const newPlan = await tx.subscriptionPlan.findFirst({
          where: { id: data.planId, isActive: true },
          include: { limits: true }
        })

        if (!newPlan) {
          throw new Error('New plan not found or inactive')
        }

        updateData.planId = data.planId
        updateData.basePrice = newPlan.pricing[data.billingCycle?.toLowerCase() || subscription.billingCycle.toLowerCase()]
        
        // Apply new plan limits
        await applyPlanLimits(storeId, newPlan.limits, tx)
      }

      // Handle billing cycle change
      if (data.billingCycle && data.billingCycle !== subscription.billingCycle) {
        updateData.billingCycle = data.billingCycle
        updateData.currentPeriodEnd = calculatePeriodEnd(subscription.currentPeriodStart, data.billingCycle)
      }

      // Handle addon changes
      if (data.addons) {
        // Remove existing addons
        await tx.subscriptionAddonUsage.deleteMany({
          where: { subscriptionId: subscription.id }
        })

        // Add new addons
        if (data.addons.length > 0) {
          const addonIds = data.addons.map(a => a.addonId)
          const addons = await tx.subscriptionAddon.findMany({
            where: { id: { in: addonIds }, isActive: true }
          })

          const addonUsages = data.addons.map(addon => {
            const addonDetails = addons.find(a => a.id === addon.addonId)!
            return {
              subscriptionId: subscription.id,
              addonId: addon.addonId,
              quantity: addon.quantity,
              unitPrice: addonDetails.pricing.monthly,
              totalPrice: addonDetails.pricing.monthly * addon.quantity,
              storeId
            }
          })

          await tx.subscriptionAddonUsage.createMany({
            data: addonUsages
          })

          // Update total price
          const addonsTotal = addonUsages.reduce((sum, addon) => sum + addon.totalPrice, 0)
          updateData.totalPrice = (updateData.basePrice || subscription.basePrice) + addonsTotal
        }
      }

      if (data.metadata) {
        updateData.metadata = data.metadata
      }

      // Update subscription
      const updatedSubscription = await tx.subscription.update({
        where: { id: subscription.id },
        data: updateData,
        include: {
          plan: true,
          addons: {
            include: {
              addon: true
            }
          }
        }
      })

      return updatedSubscription
    })

    // Create audit log
    await createAuditLog(
      'UPDATE',
      'SUBSCRIPTION',
      `Updated subscription: ${result.id}`,
      user.id,
      storeId
    )

    return createSuccessResponse(result, 'Subscription updated successfully')

  } catch (error) {
    console.error('Error updating subscription:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to update subscription',
      400
    )
  }
})

// Helper functions

function calculateDaysRemaining(subscription: any): number {
  if (subscription.status === 'CANCELLED') return 0
  
  const endDate = subscription.trialEndDate || subscription.currentPeriodEnd
  const now = new Date()
  const diffTime = new Date(endDate).getTime() - now.getTime()
  return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)))
}

function isTrialActive(subscription: any): boolean {
  if (!subscription.trialEndDate) return false
  return new Date(subscription.trialEndDate) > new Date() && subscription.status === 'TRIAL'
}

function calculateNextBillingDate(subscription: any): Date {
  if (subscription.status === 'CANCELLED') return null
  
  if (subscription.trialEndDate && new Date(subscription.trialEndDate) > new Date()) {
    return new Date(subscription.trialEndDate)
  }
  
  return new Date(subscription.currentPeriodEnd)
}

function calculateTotalMonthlyValue(subscription: any): number {
  let monthlyValue = subscription.basePrice
  
  // Convert to monthly if different billing cycle
  switch (subscription.billingCycle) {
    case 'QUARTERLY':
      monthlyValue = monthlyValue / 3
      break
    case 'YEARLY':
      monthlyValue = monthlyValue / 12
      break
  }
  
  // Add addons (assuming they're already monthly)
  if (subscription.addons) {
    monthlyValue += subscription.addons.reduce((sum: number, addon: any) => 
      sum + (addon.totalPrice || 0), 0)
  }
  
  return monthlyValue
}

async function calculateUsagePercentage(subscription: any): Promise<any> {
  // This would calculate actual usage against plan limits
  // For now, return mock data
  return {
    users: 75,
    storage: 45,
    transactions: 60,
    apiCalls: 30
  }
}

function determineRenewalStatus(subscription: any): string {
  if (subscription.status === 'CANCELLED') return 'CANCELLED'
  
  const daysToRenewal = calculateDaysRemaining(subscription)
  
  if (daysToRenewal <= 7) return 'RENEWING_SOON'
  if (daysToRenewal <= 30) return 'RENEWAL_UPCOMING'
  return 'ACTIVE'
}

function calculatePeriodEnd(startDate: Date, billingCycle: string): Date {
  const endDate = new Date(startDate)
  
  switch (billingCycle) {
    case 'MONTHLY':
      endDate.setMonth(endDate.getMonth() + 1)
      break
    case 'QUARTERLY':
      endDate.setMonth(endDate.getMonth() + 3)
      break
    case 'YEARLY':
      endDate.setFullYear(endDate.getFullYear() + 1)
      break
  }
  
  return endDate
}

async function createSubscriptionInvoice(subscription: any, tx: any) {
  const invoice = await tx.subscriptionInvoice.create({
    data: {
      subscriptionId: subscription.id,
      amount: subscription.totalPrice,
      currency: subscription.currency,
      status: 'PENDING',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      periodStart: subscription.currentPeriodStart,
      periodEnd: subscription.currentPeriodEnd,
      storeId: subscription.storeId
    }
  })
  
  return invoice
}

async function applyPlanLimits(storeId: string, limits: any, tx: any) {
  // Update store with plan limits
  await tx.store.update({
    where: { id: storeId },
    data: {
      planLimits: limits
    }
  })
}
