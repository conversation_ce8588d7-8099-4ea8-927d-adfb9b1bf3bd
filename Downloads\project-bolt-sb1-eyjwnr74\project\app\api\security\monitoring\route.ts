import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess,
  createAuditLog
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/security/monitoring - Get security monitoring dashboard
export const GET = withPermission('SECURITY', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const period = parseInt(searchParams.get('period') || '24') // hours
  const includeDetails = searchParams.get('details') === 'true'

  try {
    const startTime = new Date(Date.now() - period * 60 * 60 * 1000)

    const [
      securityEvents,
      loginAttempts,
      suspiciousActivity,
      systemHealth,
      userSessions,
      accessPatterns
    ] = await Promise.all([
      // Security events
      getSecurityEvents(storeId, startTime),
      
      // Login attempts analysis
      getLoginAttempts(storeId, startTime),
      
      // Suspicious activity detection
      getSuspiciousActivity(storeId, startTime),
      
      // System health indicators
      getSystemHealth(storeId),
      
      // Active user sessions
      getUserSessions(storeId),
      
      // Access patterns analysis
      getAccessPatterns(storeId, startTime)
    ])

    const securityScore = calculateSecurityScore({
      securityEvents,
      loginAttempts,
      suspiciousActivity,
      systemHealth
    })

    const response = {
      period: { hours: period, startTime, endTime: new Date() },
      securityScore,
      overview: {
        totalEvents: securityEvents.totalEvents,
        criticalAlerts: securityEvents.criticalEvents,
        failedLogins: loginAttempts.failed,
        suspiciousActivities: suspiciousActivity.length,
        activeSessions: userSessions.active,
        systemHealth: systemHealth.overall
      },
      events: securityEvents,
      loginAttempts,
      suspiciousActivity: includeDetails ? suspiciousActivity : suspiciousActivity.slice(0, 10),
      systemHealth,
      userSessions,
      accessPatterns,
      recommendations: generateSecurityRecommendations({
        securityEvents,
        loginAttempts,
        suspiciousActivity,
        systemHealth
      })
    }

    return createSuccessResponse(response, 'Security monitoring data retrieved successfully')

  } catch (error) {
    console.error('Error fetching security monitoring data:', error)
    return createErrorResponse('Failed to fetch security monitoring data', 500)
  }
})

// POST /api/security/monitoring/incident - Report security incident
export const POST = withPermission('SECURITY', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  const incidentSchema = z.object({
    type: z.enum(['UNAUTHORIZED_ACCESS', 'DATA_BREACH', 'MALWARE', 'PHISHING', 'DDOS', 'OTHER']),
    severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
    title: z.string().min(1, 'Title is required'),
    description: z.string().min(1, 'Description is required'),
    affectedSystems: z.array(z.string()).optional(),
    evidence: z.array(z.object({
      type: z.enum(['LOG', 'SCREENSHOT', 'FILE', 'URL']),
      content: z.string(),
      metadata: z.record(z.any()).optional()
    })).optional(),
    immediateActions: z.string().optional()
  })

  const data = incidentSchema.parse(body)

  try {
    const incident = await prisma.securityIncident.create({
      data: {
        type: data.type,
        severity: data.severity,
        title: data.title,
        description: data.description,
        affectedSystems: data.affectedSystems,
        evidence: data.evidence,
        immediateActions: data.immediateActions,
        status: 'OPEN',
        reportedById: user.id,
        storeId
      },
      include: {
        reportedBy: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      }
    })

    // Create audit log
    await createAuditLog(
      'CREATE',
      'SECURITY_INCIDENT',
      `Reported security incident: ${data.title} (${data.severity})`,
      user.id,
      storeId
    )

    // Auto-trigger security measures for critical incidents
    if (data.severity === 'CRITICAL') {
      await triggerCriticalIncidentResponse(incident, storeId)
    }

    // Notify security team
    await notifySecurityTeam(incident, storeId)

    return createSuccessResponse(incident, 'Security incident reported successfully')

  } catch (error) {
    console.error('Error reporting security incident:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to report security incident',
      400
    )
  }
})

// PUT /api/security/monitoring/lockdown - Emergency lockdown
export const PUT = withPermission('SECURITY', 'ADMIN', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  const lockdownSchema = z.object({
    reason: z.string().min(1, 'Reason is required'),
    duration: z.number().min(1).max(1440).optional(), // minutes, max 24 hours
    allowedRoles: z.array(z.string()).default(['FOUNDER', 'SUPER_ADMIN']),
    notifyUsers: z.boolean().default(true)
  })

  const { reason, duration, allowedRoles, notifyUsers } = lockdownSchema.parse(body)

  try {
    const lockdownConfig = {
      enabled: true,
      reason,
      startTime: new Date().toISOString(),
      endTime: duration ? new Date(Date.now() + duration * 60 * 1000).toISOString() : null,
      allowedRoles,
      initiatedBy: user.id
    }

    // Store lockdown configuration
    await prisma.setting.upsert({
      where: {
        storeId_category_key: {
          storeId,
          category: 'SECURITY',
          key: 'emergency_lockdown'
        }
      },
      update: {
        value: JSON.stringify(lockdownConfig),
        updatedById: user.id
      },
      create: {
        storeId,
        category: 'SECURITY',
        key: 'emergency_lockdown',
        value: JSON.stringify(lockdownConfig),
        dataType: 'JSON',
        createdById: user.id,
        updatedById: user.id
      }
    })

    // Terminate all active sessions except allowed roles
    await terminateUnauthorizedSessions(storeId, allowedRoles)

    // Create audit log
    await createAuditLog(
      'UPDATE',
      'SECURITY',
      `Emergency lockdown initiated: ${reason}`,
      user.id,
      storeId
    )

    // Notify users if requested
    if (notifyUsers) {
      await notifyLockdown(storeId, lockdownConfig)
    }

    return createSuccessResponse({
      lockdown: lockdownConfig
    }, 'Emergency lockdown initiated successfully')

  } catch (error) {
    console.error('Error initiating emergency lockdown:', error)
    return createErrorResponse('Failed to initiate emergency lockdown', 500)
  }
})

// Helper functions

async function getSecurityEvents(storeId: string, startTime: Date) {
  const events = await prisma.auditLog.findMany({
    where: {
      storeId,
      createdAt: { gte: startTime },
      OR: [
        { action: 'LOGIN' },
        { action: 'LOGOUT' },
        { severity: { in: ['HIGH', 'CRITICAL'] } },
        { entityType: { in: ['USER', 'SECURITY', 'SETTING'] } }
      ]
    },
    include: {
      user: {
        select: { id: true, name: true, role: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  const criticalEvents = events.filter(e => e.severity === 'CRITICAL')
  const highRiskEvents = events.filter(e => e.severity === 'HIGH')

  return {
    totalEvents: events.length,
    criticalEvents: criticalEvents.length,
    highRiskEvents: highRiskEvents.length,
    events: events.slice(0, 50), // Latest 50 events
    timeline: generateEventTimeline(events)
  }
}

async function getLoginAttempts(storeId: string, startTime: Date) {
  const loginLogs = await prisma.auditLog.findMany({
    where: {
      storeId,
      action: 'LOGIN',
      createdAt: { gte: startTime }
    },
    include: {
      user: {
        select: { id: true, name: true, role: true }
      }
    }
  })

  const successful = loginLogs.filter(log => 
    log.metadata && (log.metadata as any).success === true
  )
  const failed = loginLogs.filter(log => 
    log.metadata && (log.metadata as any).success === false
  )

  // Analyze failed login patterns
  const failedByIP = failed.reduce((acc: any, log) => {
    const ip = log.ipAddress
    acc[ip] = (acc[ip] || 0) + 1
    return acc
  }, {})

  const suspiciousIPs = Object.entries(failedByIP)
    .filter(([ip, count]) => (count as number) >= 5)
    .map(([ip, count]) => ({ ip, attempts: count }))

  return {
    total: loginLogs.length,
    successful: successful.length,
    failed: failed.length,
    successRate: loginLogs.length > 0 ? (successful.length / loginLogs.length) * 100 : 0,
    suspiciousIPs,
    recentFailures: failed.slice(0, 10)
  }
}

async function getSuspiciousActivity(storeId: string, startTime: Date) {
  const activities = await prisma.auditLog.findMany({
    where: {
      storeId,
      createdAt: { gte: startTime }
    },
    include: {
      user: {
        select: { id: true, name: true, role: true }
      }
    }
  })

  const suspicious = []

  // Detect unusual patterns
  const userActivity = activities.reduce((acc: any, log) => {
    if (!log.userId) return acc
    
    if (!acc[log.userId]) {
      acc[log.userId] = {
        user: log.user,
        actions: [],
        ips: new Set(),
        timeSpread: { first: log.createdAt, last: log.createdAt }
      }
    }
    
    acc[log.userId].actions.push(log)
    acc[log.userId].ips.add(log.ipAddress)
    
    if (log.createdAt < acc[log.userId].timeSpread.first) {
      acc[log.userId].timeSpread.first = log.createdAt
    }
    if (log.createdAt > acc[log.userId].timeSpread.last) {
      acc[log.userId].timeSpread.last = log.createdAt
    }
    
    return acc
  }, {})

  // Check for suspicious patterns
  Object.values(userActivity).forEach((activity: any) => {
    // Multiple IPs for same user
    if (activity.ips.size > 3) {
      suspicious.push({
        type: 'MULTIPLE_IPS',
        severity: 'MEDIUM',
        user: activity.user,
        details: `User accessed from ${activity.ips.size} different IP addresses`,
        evidence: Array.from(activity.ips)
      })
    }

    // High activity volume
    if (activity.actions.length > 100) {
      suspicious.push({
        type: 'HIGH_ACTIVITY',
        severity: 'MEDIUM',
        user: activity.user,
        details: `User performed ${activity.actions.length} actions in ${period} hours`,
        evidence: activity.actions.length
      })
    }

    // Unusual time patterns (activity outside business hours)
    const businessHours = { start: 9, end: 18 }
    const outsideBusinessHours = activity.actions.filter((action: any) => {
      const hour = new Date(action.createdAt).getHours()
      return hour < businessHours.start || hour > businessHours.end
    })

    if (outsideBusinessHours.length > 10) {
      suspicious.push({
        type: 'UNUSUAL_HOURS',
        severity: 'LOW',
        user: activity.user,
        details: `${outsideBusinessHours.length} actions performed outside business hours`,
        evidence: outsideBusinessHours.length
      })
    }
  })

  return suspicious
}

async function getSystemHealth(storeId: string) {
  const [errorCount, warningCount, criticalCount] = await Promise.all([
    prisma.auditLog.count({
      where: {
        storeId,
        severity: 'HIGH',
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }
    }),
    
    prisma.auditLog.count({
      where: {
        storeId,
        severity: 'MEDIUM',
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }
    }),
    
    prisma.auditLog.count({
      where: {
        storeId,
        severity: 'CRITICAL',
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }
    })
  ])

  const healthScore = Math.max(0, 100 - (criticalCount * 20) - (errorCount * 5) - (warningCount * 1))

  return {
    overall: healthScore >= 80 ? 'HEALTHY' : healthScore >= 60 ? 'WARNING' : 'CRITICAL',
    score: healthScore,
    indicators: {
      errors: errorCount,
      warnings: warningCount,
      critical: criticalCount
    }
  }
}

async function getUserSessions(storeId: string) {
  // This would integrate with your session management system
  // For now, return mock data based on recent login activity
  const recentLogins = await prisma.auditLog.findMany({
    where: {
      storeId,
      action: 'LOGIN',
      createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
      metadata: {
        path: ['success'],
        equals: true
      }
    },
    include: {
      user: {
        select: { id: true, name: true, role: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  return {
    active: recentLogins.length,
    sessions: recentLogins.slice(0, 20).map(login => ({
      userId: login.userId,
      user: login.user,
      loginTime: login.createdAt,
      ipAddress: login.ipAddress,
      userAgent: login.userAgent,
      location: (login.metadata as any)?.location || 'Unknown'
    }))
  }
}

async function getAccessPatterns(storeId: string, startTime: Date) {
  const logs = await prisma.auditLog.findMany({
    where: {
      storeId,
      createdAt: { gte: startTime },
      userId: { not: null }
    },
    select: {
      createdAt: true,
      action: true,
      entityType: true,
      ipAddress: true
    }
  })

  // Analyze patterns by hour
  const hourlyPattern = Array.from({ length: 24 }, (_, hour) => ({
    hour,
    activity: logs.filter(log => new Date(log.createdAt).getHours() === hour).length
  }))

  // Top IP addresses
  const ipActivity = logs.reduce((acc: any, log) => {
    acc[log.ipAddress] = (acc[log.ipAddress] || 0) + 1
    return acc
  }, {})

  const topIPs = Object.entries(ipActivity)
    .sort(([, a], [, b]) => (b as number) - (a as number))
    .slice(0, 10)
    .map(([ip, count]) => ({ ip, requests: count }))

  return {
    hourlyPattern,
    topIPs,
    totalRequests: logs.length
  }
}

function calculateSecurityScore(data: any): number {
  let score = 100

  // Deduct points for security issues
  score -= data.securityEvents.criticalEvents * 20
  score -= data.securityEvents.highRiskEvents * 10
  score -= Math.min(data.loginAttempts.failed * 2, 30)
  score -= data.suspiciousActivity.length * 5

  // System health impact
  if (data.systemHealth.overall === 'CRITICAL') score -= 30
  else if (data.systemHealth.overall === 'WARNING') score -= 15

  return Math.max(0, Math.min(100, score))
}

function generateEventTimeline(events: any[]) {
  const timeline = events.reduce((acc: any, event) => {
    const hour = new Date(event.createdAt).toISOString().substring(0, 13) + ':00:00.000Z'
    if (!acc[hour]) {
      acc[hour] = { timestamp: hour, events: 0, critical: 0 }
    }
    acc[hour].events += 1
    if (event.severity === 'CRITICAL') {
      acc[hour].critical += 1
    }
    return acc
  }, {})

  return Object.values(timeline).sort((a: any, b: any) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  )
}

function generateSecurityRecommendations(data: any): string[] {
  const recommendations = []

  if (data.loginAttempts.failed > 10) {
    recommendations.push('Consider implementing rate limiting for login attempts')
  }

  if (data.loginAttempts.suspiciousIPs.length > 0) {
    recommendations.push('Review and potentially block suspicious IP addresses')
  }

  if (data.securityEvents.criticalEvents > 0) {
    recommendations.push('Investigate critical security events immediately')
  }

  if (data.suspiciousActivity.length > 5) {
    recommendations.push('Review user activity patterns for potential security threats')
  }

  if (data.systemHealth.overall !== 'HEALTHY') {
    recommendations.push('Address system health issues to improve security posture')
  }

  return recommendations
}

async function triggerCriticalIncidentResponse(incident: any, storeId: string) {
  // Auto-lockdown for critical incidents
  if (incident.type === 'DATA_BREACH' || incident.type === 'UNAUTHORIZED_ACCESS') {
    // Implement emergency lockdown
    console.log('Triggering emergency lockdown for critical incident:', incident.id)
  }
}

async function notifySecurityTeam(incident: any, storeId: string) {
  // Get security team members
  const securityTeam = await prisma.user.findMany({
    where: {
      storeId,
      role: { in: ['FOUNDER', 'SUPER_ADMIN'] },
      isActive: true
    }
  })

  // Create notifications
  for (const user of securityTeam) {
    await prisma.notification.create({
      data: {
        title: 'Security Incident Reported',
        message: `${incident.severity} security incident: ${incident.title}`,
        type: 'ALERT',
        category: 'SECURITY',
        priority: incident.severity,
        userId: user.id,
        storeId
      }
    })
  }
}

async function terminateUnauthorizedSessions(storeId: string, allowedRoles: string[]) {
  // This would integrate with your session management system
  // For now, log the action
  console.log('Terminating unauthorized sessions for store:', storeId, 'Allowed roles:', allowedRoles)
}

async function notifyLockdown(storeId: string, config: any) {
  // Notify all users about lockdown
  const users = await prisma.user.findMany({
    where: { storeId, isActive: true }
  })

  for (const user of users) {
    await prisma.notification.create({
      data: {
        title: 'Emergency Lockdown Activated',
        message: `System lockdown initiated: ${config.reason}`,
        type: 'ERROR',
        category: 'SECURITY',
        priority: 'URGENT',
        userId: user.id,
        storeId
      }
    })
  }
}
