import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/notifications/realtime - Get real-time notification updates
export const GET = withPermission('NOTIFICATION', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const lastCheck = searchParams.get('lastCheck')
  const categories = searchParams.get('categories')?.split(',')

  try {
    // Get notifications since last check
    const where: any = {
      storeId,
      OR: [
        { userId: user.id },
        { userId: null } // Global notifications
      ]
    }

    if (lastCheck) {
      where.createdAt = { gt: new Date(lastCheck) }
    }

    if (categories && categories.length > 0) {
      where.category = { in: categories }
    }

    const [newNotifications, unreadCount, criticalAlerts] = await Promise.all([
      // New notifications since last check
      prisma.notification.findMany({
        where,
        include: {
          createdBy: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 50
      }),

      // Total unread count
      prisma.notification.count({
        where: {
          storeId,
          OR: [
            { userId: user.id },
            { userId: null }
          ],
          readAt: null
        }
      }),

      // Critical alerts
      prisma.notification.findMany({
        where: {
          storeId,
          OR: [
            { userId: user.id },
            { userId: null }
          ],
          priority: 'URGENT',
          readAt: null,
          type: { in: ['ALERT', 'ERROR'] }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      })
    ])

    // Get system status
    const systemStatus = await getSystemStatus(storeId)

    // Get recent activity summary
    const activitySummary = await getActivitySummary(storeId, user.id)

    const response = {
      newNotifications: newNotifications.map(notification => ({
        ...notification,
        timeAgo: getTimeAgo(notification.createdAt),
        isRead: !!notification.readAt
      })),
      unreadCount,
      criticalAlerts: criticalAlerts.map(alert => ({
        ...alert,
        timeAgo: getTimeAgo(alert.createdAt)
      })),
      systemStatus,
      activitySummary,
      lastUpdated: new Date()
    }

    return createSuccessResponse(response, 'Real-time notifications retrieved')

  } catch (error) {
    console.error('Error getting real-time notifications:', error)
    return createErrorResponse('Failed to get real-time notifications', 500)
  }
})

// POST /api/notifications/realtime/subscribe - Subscribe to real-time notifications
export const POST = withPermission('NOTIFICATION', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  const subscriptionSchema = z.object({
    deviceToken: z.string().optional(),
    endpoint: z.string().optional(),
    categories: z.array(z.string()).default(['SYSTEM', 'INVENTORY', 'SALES']),
    preferences: z.object({
      sound: z.boolean().default(true),
      vibration: z.boolean().default(true),
      badge: z.boolean().default(true)
    }).optional()
  })

  const { deviceToken, endpoint, categories, preferences } = subscriptionSchema.parse(body)

  try {
    // Create or update subscription
    const subscription = await prisma.notificationSubscription.upsert({
      where: {
        userId_storeId: {
          userId: user.id,
          storeId
        }
      },
      update: {
        deviceToken,
        endpoint,
        categories,
        preferences,
        isActive: true,
        lastActiveAt: new Date()
      },
      create: {
        userId: user.id,
        storeId,
        deviceToken,
        endpoint,
        categories,
        preferences,
        isActive: true,
        lastActiveAt: new Date()
      }
    })

    return createSuccessResponse(subscription, 'Subscription updated successfully')

  } catch (error) {
    console.error('Error updating subscription:', error)
    return createErrorResponse('Failed to update subscription', 500)
  }
})

// DELETE /api/notifications/realtime/subscribe - Unsubscribe from real-time notifications
export const DELETE = withPermission('NOTIFICATION', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)

  try {
    await prisma.notificationSubscription.updateMany({
      where: {
        userId: user.id,
        storeId
      },
      data: {
        isActive: false,
        lastActiveAt: new Date()
      }
    })

    return createSuccessResponse({}, 'Unsubscribed successfully')

  } catch (error) {
    console.error('Error unsubscribing:', error)
    return createErrorResponse('Failed to unsubscribe', 500)
  }
})

// Helper functions

async function getSystemStatus(storeId: string) {
  const [
    lowStockCount,
    outOfStockCount,
    pendingOrders,
    overdueInvoices,
    systemErrors
  ] = await Promise.all([
    // Low stock items
    prisma.inventory.count({
      where: {
        storeId,
        quantity: { lte: prisma.inventory.fields.reorderLevel },
        quantity: { gt: 0 }
      }
    }),

    // Out of stock items
    prisma.inventory.count({
      where: { storeId, quantity: 0 }
    }),

    // Pending orders
    prisma.purchase.count({
      where: {
        storeId,
        status: 'PENDING'
      }
    }),

    // Overdue invoices
    prisma.invoice.count({
      where: {
        storeId,
        dueDate: { lt: new Date() },
        status: { in: ['SENT', 'PARTIAL'] }
      }
    }),

    // System errors (from notifications)
    prisma.notification.count({
      where: {
        storeId,
        type: 'ERROR',
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
      }
    })
  ])

  const status = {
    overall: 'healthy' as 'healthy' | 'warning' | 'critical',
    inventory: {
      lowStock: lowStockCount,
      outOfStock: outOfStockCount,
      status: outOfStockCount > 0 ? 'critical' : lowStockCount > 5 ? 'warning' : 'healthy'
    },
    orders: {
      pending: pendingOrders,
      status: pendingOrders > 10 ? 'warning' : 'healthy'
    },
    financial: {
      overdue: overdueInvoices,
      status: overdueInvoices > 0 ? 'warning' : 'healthy'
    },
    system: {
      errors: systemErrors,
      status: systemErrors > 5 ? 'critical' : systemErrors > 0 ? 'warning' : 'healthy'
    }
  }

  // Determine overall status
  const statuses = [
    status.inventory.status,
    status.orders.status,
    status.financial.status,
    status.system.status
  ]

  if (statuses.includes('critical')) {
    status.overall = 'critical'
  } else if (statuses.includes('warning')) {
    status.overall = 'warning'
  }

  return status
}

async function getActivitySummary(storeId: string, userId: string) {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const [
    todaySales,
    todayOrders,
    todayCustomers,
    recentActivity
  ] = await Promise.all([
    // Today's sales
    prisma.sale.aggregate({
      where: {
        storeId,
        createdAt: { gte: today },
        status: 'COMPLETED'
      },
      _sum: { total: true },
      _count: true
    }),

    // Today's orders
    prisma.purchase.count({
      where: {
        storeId,
        createdAt: { gte: today }
      }
    }),

    // New customers today
    prisma.customer.count({
      where: {
        storeId,
        createdAt: { gte: today }
      }
    }),

    // Recent user activity
    prisma.auditLog.findMany({
      where: {
        storeId,
        userId,
        createdAt: { gte: new Date(Date.now() - 2 * 60 * 60 * 1000) } // Last 2 hours
      },
      orderBy: { createdAt: 'desc' },
      take: 5,
      select: {
        action: true,
        entityType: true,
        description: true,
        createdAt: true
      }
    })
  ]

  return {
    today: {
      sales: {
        amount: todaySales._sum.total || 0,
        count: todaySales._count
      },
      orders: todayOrders,
      newCustomers: todayCustomers
    },
    recentActivity: recentActivity.map(activity => ({
      ...activity,
      timeAgo: getTimeAgo(activity.createdAt)
    }))
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
  return date.toLocaleDateString()
}

// WebSocket connection handler (if using WebSocket)
export async function handleWebSocketConnection(ws: any, userId: string, storeId: string) {
  // Store WebSocket connection for real-time updates
  const connectionId = `${userId}_${storeId}_${Date.now()}`
  
  // Send initial data
  const initialData = await getInitialNotificationData(userId, storeId)
  ws.send(JSON.stringify({
    type: 'INITIAL_DATA',
    data: initialData
  }))

  // Set up periodic updates
  const interval = setInterval(async () => {
    try {
      const updates = await getNotificationUpdates(userId, storeId)
      if (updates.hasUpdates) {
        ws.send(JSON.stringify({
          type: 'UPDATES',
          data: updates
        }))
      }
    } catch (error) {
      console.error('Error sending WebSocket updates:', error)
    }
  }, 30000) // Every 30 seconds

  // Clean up on disconnect
  ws.on('close', () => {
    clearInterval(interval)
  })

  return connectionId
}

async function getInitialNotificationData(userId: string, storeId: string) {
  const [notifications, unreadCount, systemStatus] = await Promise.all([
    prisma.notification.findMany({
      where: {
        storeId,
        OR: [
          { userId },
          { userId: null }
        ]
      },
      orderBy: { createdAt: 'desc' },
      take: 20
    }),
    
    prisma.notification.count({
      where: {
        storeId,
        OR: [
          { userId },
          { userId: null }
        ],
        readAt: null
      }
    }),
    
    getSystemStatus(storeId)
  ])

  return {
    notifications,
    unreadCount,
    systemStatus
  }
}

async function getNotificationUpdates(userId: string, storeId: string) {
  // Check for new notifications in the last minute
  const lastMinute = new Date(Date.now() - 60 * 1000)
  
  const newNotifications = await prisma.notification.findMany({
    where: {
      storeId,
      OR: [
        { userId },
        { userId: null }
      ],
      createdAt: { gte: lastMinute }
    },
    orderBy: { createdAt: 'desc' }
  })

  return {
    hasUpdates: newNotifications.length > 0,
    newNotifications,
    timestamp: new Date()
  }
}
