import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const expenseSchema = z.object({
  title: z.string().min(1, 'Expense title is required'),
  description: z.string().optional(),
  amount: z.number().min(0, 'Amount must be positive'),
  category: z.enum(['RENT', 'UTILITIES', 'SALARY', 'MARKETING', 'SUPPLIES', 'MAINTENANCE', 'TRAVEL', 'INSURANCE', 'LEGAL', 'TECHNOLOGY', 'OTHER']),
  subcategory: z.string().optional(),
  date: z.string().min(1, 'Date is required'),
  paymentMethod: z.enum(['CASH', 'CARD', 'UPI', 'BANK_TRANSFER', 'CHEQUE']).default('CASH'),
  vendorName: z.string().optional(),
  vendorGST: z.string().optional(),
  taxAmount: z.number().min(0).default(0),
  taxRate: z.number().min(0).max(100).default(0),
  receiptUrl: z.string().optional(),
  receiptNumber: z.string().optional(),
  isRecurring: z.boolean().default(false),
  recurringFrequency: z.enum(['MONTHLY', 'QUARTERLY', 'YEARLY']).optional(),
  tags: z.array(z.string()).optional(),
  approvalRequired: z.boolean().default(false),
  notes: z.string().optional()
})

// GET /api/expenses - Get all expenses with advanced filtering
export const GET = withPermission('EXPENSE', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  const category = searchParams.get('category')
  const paymentMethod = searchParams.get('paymentMethod')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const minAmount = searchParams.get('minAmount')
  const maxAmount = searchParams.get('maxAmount')
  const status = searchParams.get('status') // pending, approved, rejected
  const isRecurring = searchParams.get('isRecurring')

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['title', 'description', 'vendorName', 'receiptNumber']),
    ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'date')
  }

  if (category) {
    where.category = category
  }

  if (paymentMethod) {
    where.paymentMethod = paymentMethod
  }

  if (minAmount || maxAmount) {
    where.amount = {}
    if (minAmount) where.amount.gte = parseFloat(minAmount)
    if (maxAmount) where.amount.lte = parseFloat(maxAmount)
  }

  if (status) {
    switch (status) {
      case 'pending':
        where.approvalRequired = true
        where.approvedById = null
        where.rejectedById = null
        break
      case 'approved':
        where.approvedById = { not: null }
        break
      case 'rejected':
        where.rejectedById = { not: null }
        break
    }
  }

  if (isRecurring !== null) {
    where.isRecurring = isRecurring === 'true'
  }

  // Get total count
  const total = await prisma.expense.count({ where })

  // Get expenses with pagination
  const expenses = await prisma.expense.findMany({
    where,
    include: {
      createdBy: {
        select: {
          id: true,
          name: true
        }
      },
      approvedBy: {
        select: {
          id: true,
          name: true
        }
      },
      rejectedBy: {
        select: {
          id: true,
          name: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  // Add calculated fields
  const expensesWithCalculations = expenses.map(expense => ({
    ...expense,
    totalAmount: expense.amount + expense.taxAmount,
    status: expense.rejectedById ? 'rejected' : 
            expense.approvedById ? 'approved' : 
            expense.approvalRequired ? 'pending' : 'approved',
    isOverdue: expense.isRecurring && shouldGenerateRecurring(expense)
  }))

  return createPaginatedResponse(expensesWithCalculations, page, limit, total)
})

// POST /api/expenses - Create new expense
export const POST = withPermission('EXPENSE', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = expenseSchema.parse(body)

  // Check if user can approve expenses or if approval is required
  const canApprove = ['FOUNDER', 'SUPER_ADMIN', 'ADMIN'].includes(user.role)
  const needsApproval = data.approvalRequired && !canApprove

  const expense = await prisma.expense.create({
    data: {
      ...data,
      date: new Date(data.date),
      totalAmount: data.amount + data.taxAmount,
      status: needsApproval ? 'PENDING' : 'APPROVED',
      approvedById: needsApproval ? undefined : user.id,
      approvedAt: needsApproval ? undefined : new Date(),
      createdById: user.id,
      storeId
    },
    include: {
      createdBy: {
        select: {
          id: true,
          name: true
        }
      },
      approvedBy: {
        select: {
          id: true,
          name: true
        }
      }
    }
  })

  // Create recurring expense schedule if applicable
  if (data.isRecurring && data.recurringFrequency) {
    await createRecurringSchedule(expense.id, data.recurringFrequency, storeId)
  }

  // Create notification for approval if required
  if (needsApproval) {
    const approvers = await prisma.user.findMany({
      where: {
        storeId,
        role: { in: ['FOUNDER', 'SUPER_ADMIN', 'ADMIN'] },
        isActive: true
      },
      select: { id: true }
    })

    for (const approver of approvers) {
      await prisma.notification.create({
        data: {
          title: 'Expense Approval Required',
          message: `Expense "${data.title}" (₹${data.amount}) requires your approval`,
          type: 'WARNING',
          userId: approver.id,
          storeId
        }
      })
    }
  }

  // Create audit log
  await createAuditLog(
    'CREATE',
    'EXPENSE',
    `Created expense: ${expense.title} - ₹${expense.totalAmount}`,
    user.id,
    storeId
  )

  return createSuccessResponse(expense, 'Expense created successfully')
})

// Helper functions

function shouldGenerateRecurring(expense: any): boolean {
  if (!expense.isRecurring || !expense.recurringFrequency) return false

  const lastDate = new Date(expense.date)
  const now = new Date()
  
  let nextDueDate = new Date(lastDate)
  
  switch (expense.recurringFrequency) {
    case 'MONTHLY':
      nextDueDate.setMonth(nextDueDate.getMonth() + 1)
      break
    case 'QUARTERLY':
      nextDueDate.setMonth(nextDueDate.getMonth() + 3)
      break
    case 'YEARLY':
      nextDueDate.setFullYear(nextDueDate.getFullYear() + 1)
      break
  }

  return now >= nextDueDate
}

async function createRecurringSchedule(expenseId: string, frequency: string, storeId: string) {
  // This would create a schedule for recurring expenses
  // Implementation would depend on your recurring expense requirements
  await prisma.recurringExpense.create({
    data: {
      expenseId,
      frequency,
      nextDueDate: calculateNextDueDate(new Date(), frequency),
      isActive: true,
      storeId
    }
  })
}

function calculateNextDueDate(currentDate: Date, frequency: string): Date {
  const nextDate = new Date(currentDate)
  
  switch (frequency) {
    case 'MONTHLY':
      nextDate.setMonth(nextDate.getMonth() + 1)
      break
    case 'QUARTERLY':
      nextDate.setMonth(nextDate.getMonth() + 3)
      break
    case 'YEARLY':
      nextDate.setFullYear(nextDate.getFullYear() + 1)
      break
  }

  return nextDate
}
