import { NextRequest, NextResponse } from 'next/server'
import { getAuthUser } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const saleItemSchema = z.object({
  productId: z.string(),
  quantity: z.number().min(1),
  unitPrice: z.number().min(0),
  totalPrice: z.number().min(0)
})

const saleSchema = z.object({
  customerId: z.string().optional(),
  items: z.array(saleItemSchema).min(1),
  totalAmount: z.number().min(0),
  taxAmount: z.number().min(0).default(0),
  discount: z.number().min(0).default(0),
  paidAmount: z.number().min(0),
  paymentMethod: z.enum(['CASH', 'CARD', 'UPI', 'BANK_TRANSFER', 'CREDIT']).default('CASH'),
  notes: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthUser(request)
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const storeId = user.storeId

    if (!storeId) {
      return NextResponse.json({ error: 'Store not found' }, { status: 400 })
    }

    const sales = await prisma.sale.findMany({
      where: { storeId },
      include: {
        customer: true,
        items: {
          include: {
            product: true
          }
        },
        createdBy: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 100
    })

    return NextResponse.json(sales)
  } catch (error) {
    console.error('Get sales error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getAuthUser(request)
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const storeId = user.storeId

    if (!storeId) {
      return NextResponse.json({ error: 'Store not found' }, { status: 400 })
    }

    const body = await request.json()
    const data = saleSchema.parse(body)

    // Generate sale number
    const saleCount = await prisma.sale.count({ where: { storeId } })
    const saleNo = `SAL-${String(saleCount + 1).padStart(6, '0')}`

    // Create sale with items
    const sale = await prisma.sale.create({
      data: {
        saleNo,
        customerId: data.customerId,
        totalAmount: data.totalAmount,
        taxAmount: data.taxAmount,
        discount: data.discount,
        paidAmount: data.paidAmount,
        paymentMethod: data.paymentMethod,
        status: 'COMPLETED',
        notes: data.notes,
        storeId,
        createdById: user.id,
        items: {
          create: data.items
        }
      },
      include: {
        items: {
          include: {
            product: true
          }
        },
        customer: true
      }
    })

    // Update inventory
    for (const item of data.items) {
      await prisma.inventory.updateMany({
        where: {
          productId: item.productId,
          storeId
        },
        data: {
          quantity: { decrement: item.quantity }
        }
      })
    }

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'CREATE',
        module: 'SALES',
        details: `Created sale: ${sale.saleNo} for ₹${sale.totalAmount}`,
        userId: user.id,
        storeId
      }
    })

    return NextResponse.json(sale, { status: 201 })
  } catch (error) {
    console.error('Create sale error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}