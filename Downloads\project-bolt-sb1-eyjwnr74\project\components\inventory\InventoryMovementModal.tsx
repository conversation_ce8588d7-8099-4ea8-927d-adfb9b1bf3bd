'use client'

import React, { useState } from 'react'
import { 
  X, 
  Plus, 
  Minus, 
  ArrowUpDown, 
  RefreshCw, 
  Calendar, 
  User, 
  FileText,
  TrendingUp,
  TrendingDown,
  Package,
  MapPin,
  AlertCircle
} from 'lucide-react'

interface InventoryMovement {
  id: string
  type: 'IN' | 'OUT' | 'TRANSFER' | 'ADJUSTMENT'
  quantity: number
  previousQuantity: number
  newQuantity: number
  reason: string
  reference: string
  notes: string
  fromLocation?: {
    warehouse: string
    zone: string
    rack: string
    shelf: string
  }
  toLocation?: {
    warehouse: string
    zone: string
    rack: string
    shelf: string
  }
  createdBy: {
    id: string
    name: string
  }
  createdAt: string
}

interface InventoryMovementModalProps {
  productId: string | null
  productName: string
  currentStock: number
  isOpen: boolean
  onClose: () => void
  onSubmit: (movement: any) => void
}

export default function InventoryMovementModal({ 
  productId, 
  productName, 
  currentStock, 
  isOpen, 
  onClose, 
  onSubmit 
}: InventoryMovementModalProps) {
  const [movementType, setMovementType] = useState<'IN' | 'OUT' | 'TRANSFER' | 'ADJUSTMENT'>('ADJUSTMENT')
  const [quantity, setQuantity] = useState('')
  const [reason, setReason] = useState('')
  const [reference, setReference] = useState('')
  const [notes, setNotes] = useState('')
  const [fromLocation, setFromLocation] = useState({
    warehouse: '',
    zone: '',
    rack: '',
    shelf: ''
  })
  const [toLocation, setToLocation] = useState({
    warehouse: '',
    zone: '',
    rack: '',
    shelf: ''
  })
  const [loading, setLoading] = useState(false)

  if (!isOpen) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!quantity || !reason) {
      alert('Please fill in all required fields')
      return
    }

    setLoading(true)
    
    try {
      const movementData = {
        productId,
        type: movementType,
        quantity: parseInt(quantity),
        reason,
        reference,
        notes,
        fromLocation: movementType === 'TRANSFER' ? fromLocation : undefined,
        toLocation: movementType === 'TRANSFER' ? toLocation : undefined
      }

      await onSubmit(movementData)
      
      // Reset form
      setQuantity('')
      setReason('')
      setReference('')
      setNotes('')
      setFromLocation({ warehouse: '', zone: '', rack: '', shelf: '' })
      setToLocation({ warehouse: '', zone: '', rack: '', shelf: '' })
      
      onClose()
    } catch (error) {
      console.error('Error submitting movement:', error)
    } finally {
      setLoading(false)
    }
  }

  const getNewStock = () => {
    const qty = parseInt(quantity) || 0
    switch (movementType) {
      case 'IN':
        return currentStock + qty
      case 'OUT':
        return currentStock - qty
      case 'ADJUSTMENT':
        return qty // For adjustments, quantity is the new total
      case 'TRANSFER':
        return currentStock // Transfer doesn't change total stock
      default:
        return currentStock
    }
  }

  const getMovementIcon = (type: string) => {
    switch (type) {
      case 'IN': return <TrendingUp className="w-5 h-5 text-green-600" />
      case 'OUT': return <TrendingDown className="w-5 h-5 text-red-600" />
      case 'TRANSFER': return <ArrowUpDown className="w-5 h-5 text-blue-600" />
      case 'ADJUSTMENT': return <RefreshCw className="w-5 h-5 text-purple-600" />
      default: return <Package className="w-5 h-5 text-gray-600" />
    }
  }

  const getMovementColor = (type: string) => {
    switch (type) {
      case 'IN': return 'border-green-500 bg-green-50'
      case 'OUT': return 'border-red-500 bg-red-50'
      case 'TRANSFER': return 'border-blue-500 bg-blue-50'
      case 'ADJUSTMENT': return 'border-purple-500 bg-purple-50'
      default: return 'border-gray-500 bg-gray-50'
    }
  }

  const reasonOptions = {
    IN: [
      'Purchase Receipt',
      'Return from Customer',
      'Production',
      'Transfer In',
      'Found Stock',
      'Other'
    ],
    OUT: [
      'Sale',
      'Return to Supplier',
      'Damage/Spoilage',
      'Transfer Out',
      'Theft/Loss',
      'Sampling',
      'Other'
    ],
    TRANSFER: [
      'Store Transfer',
      'Warehouse Relocation',
      'Zone Reorganization',
      'Other'
    ],
    ADJUSTMENT: [
      'Physical Count',
      'System Correction',
      'Damage Write-off',
      'Expiry Write-off',
      'Theft Write-off',
      'Other'
    ]
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Inventory Movement</h2>
            <p className="text-sm text-gray-600">{productName}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Current Stock Info */}
        <div className="p-6 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Package className="w-5 h-5 text-gray-600" />
              <span className="text-sm text-gray-600">Current Stock:</span>
              <span className="font-semibold text-gray-900">{currentStock} units</span>
            </div>
            {quantity && (
              <div className="flex items-center gap-3">
                <span className="text-sm text-gray-600">New Stock:</span>
                <span className={`font-semibold ${getNewStock() < 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {getNewStock()} units
                </span>
              </div>
            )}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Movement Type Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">Movement Type</label>
            <div className="grid grid-cols-2 gap-3">
              {(['IN', 'OUT', 'TRANSFER', 'ADJUSTMENT'] as const).map((type) => (
                <button
                  key={type}
                  type="button"
                  onClick={() => setMovementType(type)}
                  className={`p-3 border-2 rounded-lg transition-colors ${
                    movementType === type
                      ? getMovementColor(type)
                      : 'border-gray-200 bg-white hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    {getMovementIcon(type)}
                    <span className="font-medium">{type.replace('_', ' ')}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Quantity */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {movementType === 'ADJUSTMENT' ? 'New Stock Quantity' : 'Quantity'}
              <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder={movementType === 'ADJUSTMENT' ? 'Enter new total quantity' : 'Enter quantity'}
                min="0"
                required
              />
              {movementType !== 'ADJUSTMENT' && quantity && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {movementType === 'IN' ? (
                    <Plus className="w-4 h-4 text-green-600" />
                  ) : movementType === 'OUT' ? (
                    <Minus className="w-4 h-4 text-red-600" />
                  ) : (
                    <ArrowUpDown className="w-4 h-4 text-blue-600" />
                  )}
                </div>
              )}
            </div>
            {getNewStock() < 0 && (
              <div className="flex items-center gap-1 mt-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                Warning: This will result in negative stock
              </div>
            )}
          </div>

          {/* Transfer Locations */}
          {movementType === 'TRANSFER' && (
            <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">From Location</label>
                <div className="space-y-2">
                  <select
                    value={fromLocation.warehouse}
                    onChange={(e) => setFromLocation(prev => ({ ...prev, warehouse: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select Warehouse</option>
                    <option value="Main Warehouse">Main Warehouse</option>
                    <option value="Branch A">Branch A</option>
                    <option value="Branch B">Branch B</option>
                  </select>
                  <div className="grid grid-cols-3 gap-2">
                    <input
                      type="text"
                      placeholder="Zone"
                      value={fromLocation.zone}
                      onChange={(e) => setFromLocation(prev => ({ ...prev, zone: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <input
                      type="text"
                      placeholder="Rack"
                      value={fromLocation.rack}
                      onChange={(e) => setFromLocation(prev => ({ ...prev, rack: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <input
                      type="text"
                      placeholder="Shelf"
                      value={fromLocation.shelf}
                      onChange={(e) => setFromLocation(prev => ({ ...prev, shelf: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">To Location</label>
                <div className="space-y-2">
                  <select
                    value={toLocation.warehouse}
                    onChange={(e) => setToLocation(prev => ({ ...prev, warehouse: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select Warehouse</option>
                    <option value="Main Warehouse">Main Warehouse</option>
                    <option value="Branch A">Branch A</option>
                    <option value="Branch B">Branch B</option>
                  </select>
                  <div className="grid grid-cols-3 gap-2">
                    <input
                      type="text"
                      placeholder="Zone"
                      value={toLocation.zone}
                      onChange={(e) => setToLocation(prev => ({ ...prev, zone: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <input
                      type="text"
                      placeholder="Rack"
                      value={toLocation.rack}
                      onChange={(e) => setToLocation(prev => ({ ...prev, rack: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <input
                      type="text"
                      placeholder="Shelf"
                      value={toLocation.shelf}
                      onChange={(e) => setToLocation(prev => ({ ...prev, shelf: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Reason */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reason <span className="text-red-500">*</span>
            </label>
            <select
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Select reason</option>
              {reasonOptions[movementType].map((option) => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>

          {/* Reference */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Reference Number</label>
            <input
              type="text"
              value={reference}
              onChange={(e) => setReference(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="PO number, invoice number, etc."
            />
          </div>

          {/* Notes */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Additional notes or comments..."
            />
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !quantity || !reason}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Processing...' : 'Record Movement'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
