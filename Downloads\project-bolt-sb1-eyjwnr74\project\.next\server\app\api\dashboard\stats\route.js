"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_DELL_Downloads_project_bolt_sb1_eyjwnr74_project_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/stats/route.ts */ \"(rsc)/./app/api/dashboard/stats/route.ts\");\n\n// @ts-ignore this need to be imported from next/dist to be external\n\n\n// @ts-expect-error - replaced by webpack/turbopack loader\n\nconst AppRouteRouteModule = next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule;\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-sb1-eyjwnr74\\\\project\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DELL_Downloads_project_bolt_sb1_eyjwnr74_project_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/dashboard/stats/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/dashboard/stats/route.ts":
/*!******************************************!*\
  !*** ./app/api/dashboard/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nasync function GET(request) {\n    try {\n        const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.getAuthUser)(request);\n        if (!user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const storeId = user.storeId;\n        if (!storeId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Store not found\"\n            }, {\n                status: 400\n            });\n        }\n        // Get today's date range\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const tomorrow = new Date(today);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        // Get this month's date range\n        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);\n        // Fetch statistics\n        const [totalSales, totalPurchases, totalCustomers, totalProducts, lowStockItems, todaySales, thisMonthSales, pendingOrders] = await Promise.all([\n            // Total sales count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.sale.count({\n                where: {\n                    storeId,\n                    status: \"COMPLETED\"\n                }\n            }),\n            // Total purchases count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.purchase.count({\n                where: {\n                    storeId\n                }\n            }),\n            // Total customers count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.customer.count({\n                where: {\n                    storeId,\n                    isActive: true\n                }\n            }),\n            // Total products count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.product.count({\n                where: {\n                    storeId,\n                    isActive: true\n                }\n            }),\n            // Low stock items count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.inventory.count({\n                where: {\n                    storeId,\n                    OR: [\n                        {\n                            quantity: {\n                                lte: _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.inventory.fields.reorderLevel\n                            }\n                        }\n                    ]\n                }\n            }),\n            // Today's sales amount\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.sale.aggregate({\n                where: {\n                    storeId,\n                    status: \"COMPLETED\",\n                    createdAt: {\n                        gte: today,\n                        lt: tomorrow\n                    }\n                },\n                _sum: {\n                    totalAmount: true\n                }\n            }),\n            // This month's sales amount\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.sale.aggregate({\n                where: {\n                    storeId,\n                    status: \"COMPLETED\",\n                    createdAt: {\n                        gte: thisMonth,\n                        lt: nextMonth\n                    }\n                },\n                _sum: {\n                    totalAmount: true\n                }\n            }),\n            // Pending orders count\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.purchase.count({\n                where: {\n                    storeId,\n                    status: \"PENDING\"\n                }\n            })\n        ]);\n        const stats = {\n            totalSales,\n            totalPurchases,\n            totalCustomers,\n            totalProducts,\n            lowStockItems,\n            todaySales: todaySales._sum.totalAmount || 0,\n            thisMonthSales: thisMonthSales._sum.totalAmount || 0,\n            pendingOrders\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(stats);\n    } catch (error) {\n        console.error(\"Dashboard stats error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkPermission: () => (/* binding */ checkPermission),\n/* harmony export */   createAuthResponse: () => (/* binding */ createAuthResponse),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   signToken: () => (/* binding */ signToken),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function verifyPassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nfunction signToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function getAuthUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return null;\n        }\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            include: {\n                store: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        return user;\n    } catch (error) {\n        console.error(\"Auth error:\", error);\n        return null;\n    }\n}\nfunction createAuthResponse(user, token) {\n    return {\n        user: {\n            id: user.id,\n            email: user.email,\n            name: user.name,\n            role: user.role,\n            storeId: user.storeId,\n            store: user.store\n        },\n        token\n    };\n}\n// Permission-based middleware helper\nasync function checkPermission(request, module, permission) {\n    const user = await getAuthUser(request);\n    if (!user) {\n        return {\n            error: \"Unauthorized\",\n            status: 401\n        };\n    }\n    // Import permissions dynamically to avoid circular dependency\n    const { hasPermission } = await __webpack_require__.e(/*! import() */ \"_rsc_lib_permissions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./permissions */ \"(rsc)/./lib/permissions.ts\"));\n    if (!hasPermission(user.role, module, permission)) {\n        return {\n            error: \"Insufficient permissions\",\n            status: 403\n        };\n    }\n    return {\n        user,\n        error: null\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb2NlcnktbWFuYWdlbWVudC1zeXN0ZW0vLi9saWIvcHJpc21hLnRzPzk4MjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDELL%5CDownloads%5Cproject-bolt-sb1-eyjwnr74%5Cproject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();