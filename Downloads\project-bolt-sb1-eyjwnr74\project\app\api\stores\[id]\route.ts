import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// GET /api/stores/[id] - Get single store with detailed metrics
export const GET = withPermission('STORE', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]

  // Check access permissions
  if (user.role !== 'FOUNDER' && user.role !== 'SUPER_ADMIN' && user.storeId !== id) {
    return createErrorResponse('Access denied to this store', 403)
  }

  const store = await prisma.store.findUnique({
    where: { id },
    include: {
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      users: {
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true
        }
      },
      _count: {
        select: {
          products: true,
          sales: true,
          customers: true,
          suppliers: true,
          purchases: true,
          expenses: true
        }
      }
    }
  })

  if (!store) {
    return createErrorResponse('Store not found', 404)
  }

  // Get detailed metrics
  const [
    todayMetrics,
    monthMetrics,
    yearMetrics,
    inventoryMetrics,
    recentActivities
  ] = await Promise.all([
    // Today's metrics
    Promise.all([
      prisma.sale.aggregate({
        where: {
          storeId: id,
          createdAt: { gte: new Date(new Date().setHours(0, 0, 0, 0)) }
        },
        _sum: { total: true },
        _count: true
      }),
      prisma.purchase.aggregate({
        where: {
          storeId: id,
          createdAt: { gte: new Date(new Date().setHours(0, 0, 0, 0)) }
        },
        _sum: { total: true },
        _count: true
      }),
      prisma.expense.aggregate({
        where: {
          storeId: id,
          createdAt: { gte: new Date(new Date().setHours(0, 0, 0, 0)) }
        },
        _sum: { amount: true },
        _count: true
      })
    ]),
    // This month's metrics
    Promise.all([
      prisma.sale.aggregate({
        where: {
          storeId: id,
          createdAt: { gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) }
        },
        _sum: { total: true },
        _count: true
      }),
      prisma.purchase.aggregate({
        where: {
          storeId: id,
          createdAt: { gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) }
        },
        _sum: { total: true },
        _count: true
      })
    ]),
    // This year's metrics
    Promise.all([
      prisma.sale.aggregate({
        where: {
          storeId: id,
          createdAt: { gte: new Date(new Date().getFullYear(), 0, 1) }
        },
        _sum: { total: true },
        _count: true
      })
    ]),
    // Inventory metrics
    Promise.all([
      prisma.inventory.count({
        where: {
          storeId: id,
          quantity: { lte: prisma.inventory.fields.reorderLevel }
        }
      }),
      prisma.inventory.count({
        where: { storeId: id, quantity: 0 }
      }),
      prisma.inventory.aggregate({
        where: { storeId: id },
        _sum: { quantity: true }
      })
    ]),
    // Recent activities
    Promise.all([
      prisma.sale.findMany({
        where: { storeId: id },
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          total: true,
          createdAt: true,
          customer: { select: { name: true } }
        }
      }),
      prisma.purchase.findMany({
        where: { storeId: id },
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          total: true,
          createdAt: true,
          supplier: { select: { name: true } }
        }
      })
    ])
  ])

  const storeWithMetrics = {
    ...store,
    metrics: {
      today: {
        sales: todayMetrics[0]._sum.total || 0,
        salesCount: todayMetrics[0]._count,
        purchases: todayMetrics[1]._sum.total || 0,
        purchasesCount: todayMetrics[1]._count,
        expenses: todayMetrics[2]._sum.amount || 0,
        expensesCount: todayMetrics[2]._count
      },
      month: {
        sales: monthMetrics[0]._sum.total || 0,
        salesCount: monthMetrics[0]._count,
        purchases: monthMetrics[1]._sum.total || 0,
        purchasesCount: monthMetrics[1]._count
      },
      year: {
        sales: yearMetrics[0]._sum.total || 0,
        salesCount: yearMetrics[0]._count
      },
      inventory: {
        lowStock: inventoryMetrics[0],
        outOfStock: inventoryMetrics[1],
        totalQuantity: inventoryMetrics[2]._sum.quantity || 0
      },
      recentActivities: {
        sales: recentActivities[0],
        purchases: recentActivities[1]
      }
    }
  }

  return createSuccessResponse(storeWithMetrics)
})

// PUT /api/stores/[id] - Update store
export const PUT = withPermission('STORE', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]
  const body = await request.json()

  // Check access permissions
  if (user.role !== 'FOUNDER' && user.role !== 'SUPER_ADMIN' && user.storeId !== id) {
    return createErrorResponse('Access denied to this store', 403)
  }

  const schema = z.object({
    name: z.string().min(1, 'Store name is required').optional(),
    address: z.string().min(1, 'Address is required').optional(),
    phone: z.string().min(1, 'Phone is required').optional(),
    email: z.string().email('Valid email is required').optional(),
    gstNumber: z.string().optional(),
    region: z.string().optional(),
    storeType: z.enum(['RETAIL', 'WHOLESALE', 'HYBRID']).optional(),
    managerName: z.string().optional(),
    managerPhone: z.string().optional(),
    operatingHours: z.object({
      monday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      tuesday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      wednesday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      thursday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      friday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      saturday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) }),
      sunday: z.object({ open: z.string(), close: z.string(), closed: z.boolean().default(false) })
    }).optional(),
    settings: z.object({
      currency: z.string().optional(),
      taxEnabled: z.boolean().optional(),
      loyaltyEnabled: z.boolean().optional(),
      discountEnabled: z.boolean().optional(),
      barcodeEnabled: z.boolean().optional()
    }).optional(),
    isActive: z.boolean().optional()
  })

  const validatedData = schema.parse(body)

  // Check if store exists
  const existingStore = await prisma.store.findUnique({
    where: { id }
  })

  if (!existingStore) {
    return createErrorResponse('Store not found', 404)
  }

  // Check if name is being changed and if it conflicts
  if (validatedData.name && validatedData.name !== existingStore.name) {
    const nameConflict = await prisma.store.findFirst({
      where: { 
        name: validatedData.name,
        id: { not: id }
      }
    })

    if (nameConflict) {
      return createErrorResponse('Store with this name already exists', 400)
    }
  }

  const updatedStore = await prisma.store.update({
    where: { id },
    data: validatedData,
    include: {
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'UPDATE',
    'STORE',
    `Updated store: ${updatedStore.name}`,
    user.id,
    id
  )

  return createSuccessResponse(updatedStore, 'Store updated successfully')
})

// DELETE /api/stores/[id] - Delete store (soft delete)
export const DELETE = withPermission('STORE', 'DELETE', async (request: NextRequest, user: any, context?: any) => {
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]

  // Only founder can delete stores
  if (user.role !== 'FOUNDER') {
    return createErrorResponse('Only founder can delete stores', 403)
  }

  // Check if store exists
  const existingStore = await prisma.store.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          users: true,
          products: true,
          sales: true
        }
      }
    }
  })

  if (!existingStore) {
    return createErrorResponse('Store not found', 404)
  }

  // Check if store has active data
  if (existingStore._count.users > 0 || existingStore._count.products > 0 || existingStore._count.sales > 0) {
    return createErrorResponse('Cannot delete store with existing data. Please transfer or remove all associated data first.', 400)
  }

  // Soft delete
  await prisma.store.update({
    where: { id },
    data: { 
      isActive: false,
      deletedAt: new Date()
    }
  })

  // Create audit log
  await createAuditLog(
    'DELETE',
    'STORE',
    `Deleted store: ${existingStore.name}`,
    user.id,
    id
  )

  return createSuccessResponse(null, 'Store deleted successfully')
})
