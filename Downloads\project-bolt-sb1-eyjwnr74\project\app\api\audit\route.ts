import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  validateStoreAccess,
  buildDateRangeFilter
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const auditFilterSchema = z.object({
  actions: z.array(z.enum(['CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT'])).optional(),
  entityTypes: z.array(z.string()).optional(),
  userIds: z.array(z.string()).optional(),
  ipAddress: z.string().optional(),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  includeSystemActions: z.boolean().default(false)
})

// GET /api/audit - Get audit logs with advanced filtering
export const GET = withPermission('AUDIT', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)
  const { searchParams } = new URL(request.url)
  
  // Parse filters
  const actions = searchParams.get('actions')?.split(',')
  const entityTypes = searchParams.get('entityTypes')?.split(',')
  const userIds = searchParams.get('userIds')?.split(',')
  const ipAddress = searchParams.get('ipAddress')
  const severity = searchParams.get('severity')
  const startDate = searchParams.get('startDate')
  const endDate = searchParams.get('endDate')
  const includeSystemActions = searchParams.get('includeSystemActions') === 'true'

  try {
    // Build filters
    const where: any = {
      storeId,
      ...buildSearchFilter(search, ['description', 'entityType']),
      ...buildDateRangeFilter(startDate || undefined, endDate || undefined, 'createdAt')
    }

    if (actions && actions.length > 0) {
      where.action = { in: actions }
    }

    if (entityTypes && entityTypes.length > 0) {
      where.entityType = { in: entityTypes }
    }

    if (userIds && userIds.length > 0) {
      where.userId = { in: userIds }
    }

    if (ipAddress) {
      where.ipAddress = ipAddress
    }

    if (severity) {
      where.severity = severity
    }

    if (!includeSystemActions) {
      where.userId = { not: null } // Exclude system-generated actions
    }

    // Get total count
    const total = await prisma.auditLog.count({ where })

    // Get audit logs with pagination
    const auditLogs = await prisma.auditLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    })

    // Add calculated fields
    const auditLogsWithCalculations = auditLogs.map(log => ({
      ...log,
      timeAgo: getTimeAgo(log.createdAt),
      riskLevel: calculateRiskLevel(log),
      isSystemAction: !log.userId,
      sessionInfo: parseSessionInfo(log.metadata)
    }))

    // Get summary statistics
    const summary = await getAuditSummary(storeId, where)

    return createPaginatedResponse(auditLogsWithCalculations, page, limit, total, { summary })

  } catch (error) {
    console.error('Error fetching audit logs:', error)
    return createErrorResponse('Failed to fetch audit logs', 500)
  }
})

// POST /api/audit - Create audit log entry (for manual logging)
export const POST = withPermission('AUDIT', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()

  const auditLogSchema = z.object({
    action: z.enum(['CREATE', 'READ', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'CUSTOM']),
    entityType: z.string().min(1, 'Entity type is required'),
    entityId: z.string().optional(),
    description: z.string().min(1, 'Description is required'),
    severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).default('LOW'),
    metadata: z.record(z.any()).optional()
  })

  const data = auditLogSchema.parse(body)

  try {
    const auditLog = await prisma.auditLog.create({
      data: {
        action: data.action,
        entityType: data.entityType,
        entityId: data.entityId,
        description: data.description,
        severity: data.severity,
        metadata: data.metadata,
        userId: user.id,
        ipAddress: getClientIP(request),
        userAgent: request.headers.get('user-agent') || '',
        storeId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      }
    })

    return createSuccessResponse(auditLog, 'Audit log created successfully')

  } catch (error) {
    console.error('Error creating audit log:', error)
    return createErrorResponse(
      error instanceof Error ? error.message : 'Failed to create audit log',
      400
    )
  }
})

// Helper functions

async function getAuditSummary(storeId: string, baseWhere: any) {
  const [
    totalLogs,
    todayLogs,
    criticalLogs,
    actionBreakdown,
    userActivity,
    entityBreakdown
  ] = await Promise.all([
    // Total logs count
    prisma.auditLog.count({ where: baseWhere }),

    // Today's logs
    prisma.auditLog.count({
      where: {
        ...baseWhere,
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0))
        }
      }
    }),

    // Critical logs in last 24 hours
    prisma.auditLog.count({
      where: {
        ...baseWhere,
        severity: 'CRITICAL',
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    }),

    // Action breakdown
    prisma.auditLog.groupBy({
      by: ['action'],
      where: baseWhere,
      _count: true,
      orderBy: { _count: { action: 'desc' } }
    }),

    // Most active users
    prisma.auditLog.groupBy({
      by: ['userId'],
      where: {
        ...baseWhere,
        userId: { not: null }
      },
      _count: true,
      orderBy: { _count: { userId: 'desc' } },
      take: 5
    }).then(async (users) => {
      const userIds = users.map(u => u.userId).filter(Boolean)
      const userDetails = await prisma.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, name: true, role: true }
      })
      
      return users.map(user => ({
        user: userDetails.find(u => u.id === user.userId),
        activityCount: user._count
      }))
    }),

    // Entity type breakdown
    prisma.auditLog.groupBy({
      by: ['entityType'],
      where: baseWhere,
      _count: true,
      orderBy: { _count: { entityType: 'desc' } },
      take: 10
    })
  ])

  return {
    totalLogs,
    todayLogs,
    criticalLogs,
    actionBreakdown: actionBreakdown.map(item => ({
      action: item.action,
      count: item._count
    })),
    topUsers: userActivity,
    entityBreakdown: entityBreakdown.map(item => ({
      entityType: item.entityType,
      count: item._count
    }))
  }
}

function calculateRiskLevel(log: any): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  // Risk calculation based on various factors
  let riskScore = 0

  // Base risk by action
  const actionRisk = {
    'DELETE': 3,
    'UPDATE': 2,
    'CREATE': 1,
    'READ': 0,
    'LOGIN': 1,
    'LOGOUT': 0,
    'EXPORT': 2,
    'IMPORT': 3
  }

  riskScore += actionRisk[log.action as keyof typeof actionRisk] || 0

  // Severity multiplier
  const severityMultiplier = {
    'LOW': 1,
    'MEDIUM': 2,
    'HIGH': 3,
    'CRITICAL': 4
  }

  riskScore *= severityMultiplier[log.severity as keyof typeof severityMultiplier] || 1

  // Time-based risk (recent actions are riskier)
  const hoursSinceAction = (Date.now() - new Date(log.createdAt).getTime()) / (1000 * 60 * 60)
  if (hoursSinceAction < 1) riskScore += 2
  else if (hoursSinceAction < 24) riskScore += 1

  // Entity type risk
  const highRiskEntities = ['USER', 'SETTING', 'PAYMENT', 'FINANCIAL']
  if (highRiskEntities.includes(log.entityType)) {
    riskScore += 2
  }

  // Convert score to risk level
  if (riskScore >= 10) return 'CRITICAL'
  if (riskScore >= 7) return 'HIGH'
  if (riskScore >= 4) return 'MEDIUM'
  return 'LOW'
}

function parseSessionInfo(metadata: any): any {
  if (!metadata) return null

  return {
    sessionId: metadata.sessionId,
    deviceType: metadata.deviceType,
    browser: metadata.browser,
    os: metadata.os,
    location: metadata.location
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
  return date.toLocaleDateString()
}

function getClientIP(request: NextRequest): string {
  // Try to get real IP from various headers
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfConnectingIP = request.headers.get('cf-connecting-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP
  }
  
  // Fallback to connection remote address
  return request.ip || 'unknown'
}
