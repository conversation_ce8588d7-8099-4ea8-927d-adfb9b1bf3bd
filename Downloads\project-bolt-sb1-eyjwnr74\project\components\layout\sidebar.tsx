'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  Truck,
  Receipt,
  BarChart3,
  Settings,
  Store,
  Tags,
  Warehouse,
  CreditCard,
  FileText,
  Bell,
  HelpCircle,
  LogOut,
  Menu,
  X,
  ChevronDown,
  Building2,
  UserCheck,
  TrendingUp,
  DollarSign
} from 'lucide-react'
import { Role } from '@/lib/types'
import { getUserModules } from '@/lib/permissions'

interface SidebarProps {
  userRole: Role
  onLogout: () => void
}

interface NavItem {
  title: string
  href: string
  icon: any
  module: string
  children?: NavItem[]
}

const navigationItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    module: 'DASHBOARD'
  },
  {
    title: 'Store Management',
    href: '/stores',
    icon: Store,
    module: 'STORE'
  },
  {
    title: 'User Management',
    href: '/users',
    icon: Users,
    module: 'USER'
  },
  {
    title: 'Products',
    href: '/products',
    icon: Package,
    module: 'PRODUCT',
    children: [
      { title: 'All Products', href: '/products', icon: Package, module: 'PRODUCT' },
      { title: 'Categories', href: '/categories', icon: Tags, module: 'CATEGORY' },
      { title: 'Inventory', href: '/inventory', icon: Warehouse, module: 'INVENTORY' }
    ]
  },
  {
    title: 'Sales',
    href: '/sales',
    icon: ShoppingCart,
    module: 'SALES',
    children: [
      { title: 'Point of Sale', href: '/pos', icon: Receipt, module: 'SALES' },
      { title: 'Sales History', href: '/sales', icon: ShoppingCart, module: 'SALES' },
      { title: 'Sales Returns', href: '/sales-returns', icon: TrendingUp, module: 'SALES_RETURN' }
    ]
  },
  {
    title: 'Purchases',
    href: '/purchases',
    icon: Truck,
    module: 'PURCHASE',
    children: [
      { title: 'Purchase Orders', href: '/purchases', icon: Truck, module: 'PURCHASE' },
      { title: 'Purchase Returns', href: '/purchase-returns', icon: TrendingUp, module: 'PURCHASE_RETURN' }
    ]
  },
  {
    title: 'Customers',
    href: '/customers',
    icon: Users,
    module: 'CUSTOMER'
  },
  {
    title: 'Suppliers',
    href: '/suppliers',
    icon: Building2,
    module: 'SUPPLIER'
  },
  {
    title: 'Expenses',
    href: '/expenses',
    icon: DollarSign,
    module: 'EXPENSE'
  },
  {
    title: 'B2B Orders',
    href: '/b2b',
    icon: Building2,
    module: 'B2B'
  },
  {
    title: 'Reports',
    href: '/reports',
    icon: BarChart3,
    module: 'REPORTS'
  },
  {
    title: 'Notifications',
    href: '/notifications',
    icon: Bell,
    module: 'NOTIFICATION'
  },
  {
    title: 'Documents',
    href: '/documents',
    icon: FileText,
    module: 'DOCUMENT'
  },
  {
    title: 'Support',
    href: '/support',
    icon: HelpCircle,
    module: 'SUPPORT'
  },
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings,
    module: 'SETTINGS'
  }
]

export function Sidebar({ userRole, onLogout }: SidebarProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const pathname = usePathname()

  const userModules = getUserModules(userRole)
  const filteredNavItems = navigationItems.filter(item => 
    userModules.includes(item.module as any)
  )

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const NavLink = ({ item, isChild = false }: { item: NavItem; isChild?: boolean }) => {
    const isActive = pathname === item.href
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.title)

    if (hasChildren) {
      return (
        <div className="space-y-1">
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-between h-12 px-4",
              isChild && "pl-8",
              "text-left font-normal hover:bg-accent hover:text-accent-foreground"
            )}
            onClick={() => toggleExpanded(item.title)}
          >
            <div className="flex items-center">
              <item.icon className="mr-3 h-5 w-5" />
              {item.title}
            </div>
            <ChevronDown className={cn(
              "h-4 w-4 transition-transform",
              isExpanded && "rotate-180"
            )} />
          </Button>
          {isExpanded && (
            <div className="space-y-1 ml-4">
              {item.children.map((child) => (
                <NavLink key={child.href} item={child} isChild />
              ))}
            </div>
          )}
        </div>
      )
    }

    return (
      <Button
        variant={isActive ? "secondary" : "ghost"}
        className={cn(
          "w-full justify-start h-12 px-4",
          isChild && "pl-8",
          isActive && "bg-secondary text-secondary-foreground",
          "text-left font-normal hover:bg-accent hover:text-accent-foreground"
        )}
        asChild
      >
        <Link href={item.href}>
          <item.icon className="mr-3 h-5 w-5" />
          {item.title}
        </Link>
      </Button>
    )
  }

  return (
    <>
      {/* Mobile toggle */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/20 z-40 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed left-0 top-0 z-40 h-full w-72 bg-background border-r transform transition-transform duration-200 ease-in-out md:relative md:transform-none",
        isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
      )}>
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex h-16 items-center border-b px-6">
            <div className="flex items-center space-x-2">
              <Store className="h-6 w-6 text-primary" />
              <span className="text-lg font-semibold">GroceryPOS</span>
            </div>
          </div>

          {/* Navigation */}
          <ScrollArea className="flex-1 px-4 py-4">
            <nav className="space-y-2">
              {filteredNavItems.map((item) => (
                <NavLink key={item.href} item={item} />
              ))}
            </nav>
          </ScrollArea>

          {/* Footer */}
          <div className="border-t p-4">
            <div className="flex items-center space-x-2 mb-4">
              <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                <UserCheck className="h-4 w-4 text-primary-foreground" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">Current Role</p>
                <p className="text-xs text-muted-foreground">{userRole.replace('_', ' ')}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start text-red-600 hover:text-red-600 hover:bg-red-50"
              onClick={onLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}