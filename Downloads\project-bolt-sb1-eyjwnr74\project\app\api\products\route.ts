import { NextRequest, NextResponse } from 'next/server'
import { getAuthUser } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const productSchema = z.object({
  name: z.string().min(1),
  sku: z.string().min(1),
  description: z.string().optional(),
  barcode: z.string().optional(),
  unit: z.string().default('pcs'),
  costPrice: z.number().min(0),
  sellingPrice: z.number().min(0),
  mrp: z.number().optional(),
  taxRate: z.number().min(0).max(100).default(0),
  minStock: z.number().min(0).default(0),
  categoryId: z.string(),
})

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthUser(request)
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const storeId = user.storeId

    if (!storeId) {
      return NextResponse.json({ error: 'Store not found' }, { status: 400 })
    }

    const products = await prisma.product.findMany({
      where: { storeId, isActive: true },
      include: {
        category: true,
        inventory: true
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(products)
  } catch (error) {
    console.error('Get products error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getAuthUser(request)
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const storeId = user.storeId

    if (!storeId) {
      return NextResponse.json({ error: 'Store not found' }, { status: 400 })
    }

    const body = await request.json()
    const data = productSchema.parse(body)

    // Check if SKU already exists
    const existingProduct = await prisma.product.findUnique({
      where: { sku: data.sku }
    })

    if (existingProduct) {
      return NextResponse.json(
        { error: 'SKU already exists' },
        { status: 400 }
      )
    }

    const product = await prisma.product.create({
      data: {
        ...data,
        storeId
      },
      include: {
        category: true
      }
    })

    // Create initial inventory record
    await prisma.inventory.create({
      data: {
        productId: product.id,
        storeId,
        quantity: 0,
        reorderLevel: data.minStock
      }
    })

    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'CREATE',
        module: 'PRODUCT',
        details: `Created product: ${product.name}`,
        userId: user.id,
        storeId
      }
    })

    return NextResponse.json(product, { status: 201 })
  } catch (error) {
    console.error('Create product error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}