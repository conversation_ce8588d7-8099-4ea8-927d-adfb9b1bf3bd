import { NextRequest, NextResponse } from 'next/server'
import { getAuthUser } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthUser(request)
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const storeId = user.storeId

    if (!storeId) {
      return NextResponse.json({ error: 'Store not found' }, { status: 400 })
    }

    // Get today's date range
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // Get this month's date range
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1)

    // Fetch statistics
    const [
      totalSales,
      totalPurchases,
      totalCustomers,
      totalProducts,
      lowStockItems,
      todaySales,
      thisMonthSales,
      pendingOrders
    ] = await Promise.all([
      // Total sales count
      prisma.sale.count({
        where: { storeId, status: 'COMPLETED' }
      }),
      
      // Total purchases count
      prisma.purchase.count({
        where: { storeId }
      }),
      
      // Total customers count
      prisma.customer.count({
        where: { storeId, isActive: true }
      }),
      
      // Total products count
      prisma.product.count({
        where: { storeId, isActive: true }
      }),
      
      // Low stock items count
      prisma.inventory.count({
        where: {
          storeId,
          OR: [
            { quantity: { lte: prisma.inventory.fields.reorderLevel } }
          ]
        }
      }),
      
      // Today's sales amount
      prisma.sale.aggregate({
        where: {
          storeId,
          status: 'COMPLETED',
          createdAt: {
            gte: today,
            lt: tomorrow
          }
        },
        _sum: { totalAmount: true }
      }),
      
      // This month's sales amount
      prisma.sale.aggregate({
        where: {
          storeId,
          status: 'COMPLETED',
          createdAt: {
            gte: thisMonth,
            lt: nextMonth
          }
        },
        _sum: { totalAmount: true }
      }),
      
      // Pending orders count
      prisma.purchase.count({
        where: { storeId, status: 'PENDING' }
      })
    ])

    const stats = {
      totalSales,
      totalPurchases,
      totalCustomers,
      totalProducts,
      lowStockItems,
      todaySales: todaySales._sum.totalAmount || 0,
      thisMonthSales: thisMonthSales._sum.totalAmount || 0,
      pendingOrders
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Dashboard stats error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}