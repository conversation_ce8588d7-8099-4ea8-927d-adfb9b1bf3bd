import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import bcrypt from 'bcryptjs'

// POST /api/users/[id]/reset-password - Reset user password
export const POST = withPermission('USER', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 2] // Get id from path (before reset-password)
  const body = await request.json()

  const schema = z.object({
    newPassword: z.string().min(6, 'Password must be at least 6 characters')
  })

  const { newPassword } = schema.parse(body)

  // Check if user exists
  const existingUser = await prisma.user.findFirst({
    where: {
      id,
      storeId
    }
  })

  if (!existingUser) {
    return createErrorResponse('User not found', 404)
  }

  // Hash new password
  const hashedPassword = await bcrypt.hash(newPassword, 12)

  // Update password
  await prisma.user.update({
    where: { id },
    data: { password: hashedPassword }
  })

  // Create audit log
  await createAuditLog(
    'UPDATE',
    'USER',
    `Reset password for user: ${existingUser.name} (${existingUser.email})`,
    user.id,
    storeId
  )

  return createSuccessResponse(null, 'Password reset successfully')
})
