import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/products/barcode/[code] - Find product by barcode
export const GET = withPermission('PRODUCT', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const barcode = pathSegments[pathSegments.length - 1]

  if (!barcode) {
    return createErrorResponse('Barcode is required', 400)
  }

  // Search by barcode or SKU
  const product = await prisma.product.findFirst({
    where: {
      OR: [
        { barcode: barcode },
        { sku: barcode }
      ],
      storeId,
      isActive: true
    },
    include: {
      category: {
        select: {
          id: true,
          name: true
        }
      },
      inventory: {
        select: {
          quantity: true,
          reorderLevel: true
        }
      },
      variants: {
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          sku: true,
          barcode: true,
          sellingPrice: true,
          costPrice: true
        }
      }
    }
  })

  if (!product) {
    return createErrorResponse('Product not found', 404)
  }

  // Check if product is in stock
  const inventory = product.inventory
  const stockStatus = inventory ? (
    inventory.quantity === 0 ? 'out_of_stock' :
    inventory.quantity <= inventory.reorderLevel ? 'low_stock' : 'in_stock'
  ) : 'no_inventory'

  const productWithStock = {
    ...product,
    stockStatus,
    stockQuantity: inventory?.quantity || 0,
    reorderLevel: inventory?.reorderLevel || 0
  }

  return createSuccessResponse(productWithStock, 'Product found')
})

// POST /api/products/barcode/[code] - Generate barcode for product
export const POST = withPermission('PRODUCT', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const productId = pathSegments[pathSegments.length - 1]
  const body = await request.json()

  const { barcode } = body

  if (!barcode) {
    return createErrorResponse('Barcode is required', 400)
  }

  // Check if product exists
  const product = await prisma.product.findFirst({
    where: {
      id: productId,
      storeId,
      isActive: true
    }
  })

  if (!product) {
    return createErrorResponse('Product not found', 404)
  }

  // Check if barcode already exists
  const existingProduct = await prisma.product.findFirst({
    where: {
      barcode: barcode,
      storeId,
      isActive: true,
      id: { not: productId }
    }
  })

  if (existingProduct) {
    return createErrorResponse('Barcode already exists for another product', 400)
  }

  // Update product with barcode
  const updatedProduct = await prisma.product.update({
    where: { id: productId },
    data: { barcode },
    include: {
      category: {
        select: {
          id: true,
          name: true
        }
      },
      inventory: {
        select: {
          quantity: true,
          reorderLevel: true
        }
      }
    }
  })

  return createSuccessResponse(updatedProduct, 'Barcode updated successfully')
})
