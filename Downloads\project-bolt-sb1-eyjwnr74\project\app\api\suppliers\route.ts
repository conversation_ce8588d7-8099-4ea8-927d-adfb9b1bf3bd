import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  getPaginationParams,
  buildSearchFilter,
  createPaginatedResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const supplierSchema = z.object({
  name: z.string().min(1, 'Supplier name is required'),
  phone: z.string().min(1, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  email: z.string().email().optional().or(z.literal('')),
  gstNumber: z.string().optional(),
  isActive: z.boolean().default(true)
})

// GET /api/suppliers - Get all suppliers with pagination and filters
export const GET = withPermission('SUPPLIER', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { page, limit, skip, search, sortBy, sortOrder } = getPaginationParams(request)

  // Build filters
  const where: any = {
    storeId,
    ...buildSearchFilter(search, ['name', 'phone', 'email', 'gstNumber'])
  }

  // Get total count
  const total = await prisma.supplier.count({ where })

  // Get suppliers with pagination
  const suppliers = await prisma.supplier.findMany({
    where,
    include: {
      _count: {
        select: {
          purchases: true
        }
      }
    },
    orderBy: { [sortBy]: sortOrder },
    skip,
    take: limit
  })

  return createPaginatedResponse(suppliers, page, limit, total)
})

// POST /api/suppliers - Create new supplier
export const POST = withPermission('SUPPLIER', 'CREATE', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const body = await request.json()
  const data = supplierSchema.parse(body)

  // Check if phone already exists (if provided)
  if (data.phone) {
    const existingSupplier = await prisma.supplier.findFirst({
      where: {
        phone: data.phone,
        storeId,
        isActive: true
      }
    })

    if (existingSupplier) {
      return createErrorResponse('Supplier with this phone number already exists', 400)
    }
  }

  // Check if email already exists (if provided)
  if (data.email) {
    const existingSupplier = await prisma.supplier.findFirst({
      where: {
        email: data.email,
        storeId,
        isActive: true
      }
    })

    if (existingSupplier) {
      return createErrorResponse('Supplier with this email already exists', 400)
    }
  }

  const supplier = await prisma.supplier.create({
    data: {
      ...data,
      storeId
    },
    include: {
      _count: {
        select: {
          purchases: true
        }
      }
    }
  })

  // Create audit log
  await createAuditLog(
    'CREATE',
    'SUPPLIER',
    `Created supplier: ${supplier.name}`,
    user.id,
    storeId
  )

  return createSuccessResponse(supplier, 'Supplier created successfully')
})
