import { NextRequest } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'

// GET /api/sales/analytics - Get comprehensive sales analytics
export const GET = withPermission('SALES', 'READ', async (request: NextRequest, user: any) => {
  const storeId = validateStoreAccess(user)
  const { searchParams } = new URL(request.url)
  const period = searchParams.get('period') || '30' // days
  const includeComparison = searchParams.get('comparison') === 'true'
  const groupBy = searchParams.get('groupBy') || 'day' // day, week, month

  const periodDays = parseInt(period)
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - periodDays)

  try {
    const [
      salesSummary,
      salesTrends,
      topProducts,
      topCategories,
      paymentMethods,
      hourlyAnalysis,
      customerAnalysis,
      comparisonData
    ] = await Promise.all([
      // Sales Summary
      getSalesSummary(storeId, startDate),
      
      // Sales Trends
      getSalesTrends(storeId, startDate, groupBy),
      
      // Top Products
      getTopProducts(storeId, startDate, 10),
      
      // Top Categories
      getTopCategories(storeId, startDate),
      
      // Payment Methods Analysis
      getPaymentMethodsAnalysis(storeId, startDate),
      
      // Hourly Analysis
      getHourlyAnalysis(storeId, startDate),
      
      // Customer Analysis
      getCustomerAnalysis(storeId, startDate),
      
      // Comparison with previous period (if requested)
      includeComparison ? getComparisonData(storeId, startDate, periodDays) : null
    ])

    const analytics = {
      period: {
        days: periodDays,
        startDate,
        endDate: new Date()
      },
      summary: salesSummary,
      trends: salesTrends,
      topProducts,
      topCategories,
      paymentMethods,
      hourlyAnalysis,
      customerAnalysis,
      ...(comparisonData && { comparison: comparisonData })
    }

    return createSuccessResponse(analytics, 'Sales analytics retrieved successfully')

  } catch (error) {
    console.error('Error fetching sales analytics:', error)
    return createErrorResponse('Failed to fetch sales analytics', 500)
  }
})

// Helper functions for analytics

async function getSalesSummary(storeId: string, startDate: Date) {
  const [totalSales, salesCount, avgOrderValue, totalProfit] = await Promise.all([
    // Total sales amount
    prisma.sale.aggregate({
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED'
      },
      _sum: { total: true }
    }),
    
    // Number of sales
    prisma.sale.count({
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED'
      }
    }),
    
    // Average order value
    prisma.sale.aggregate({
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED'
      },
      _avg: { total: true }
    }),
    
    // Total profit (selling price - cost price)
    prisma.saleItem.aggregate({
      where: {
        sale: {
          storeId,
          createdAt: { gte: startDate },
          status: 'COMPLETED'
        }
      },
      _sum: { totalPrice: true }
    }).then(async (saleItems) => {
      const items = await prisma.saleItem.findMany({
        where: {
          sale: {
            storeId,
            createdAt: { gte: startDate },
            status: 'COMPLETED'
          }
        },
        include: {
          product: {
            select: { costPrice: true }
          }
        }
      })
      
      return items.reduce((profit, item) => {
        const costPrice = item.product.costPrice * item.quantity
        return profit + (item.totalPrice - costPrice)
      }, 0)
    })
  ])

  return {
    totalSales: totalSales._sum.total || 0,
    salesCount,
    avgOrderValue: avgOrderValue._avg.total || 0,
    totalProfit
  }
}

async function getSalesTrends(storeId: string, startDate: Date, groupBy: string) {
  const sales = await prisma.sale.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate },
      status: 'COMPLETED'
    },
    select: {
      createdAt: true,
      total: true
    },
    orderBy: { createdAt: 'asc' }
  })

  const trendsMap = new Map()
  
  sales.forEach(sale => {
    let key: string
    const date = new Date(sale.createdAt)
    
    switch (groupBy) {
      case 'hour':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`
        break
      case 'week':
        const weekStart = new Date(date)
        weekStart.setDate(date.getDate() - date.getDay())
        key = weekStart.toISOString().split('T')[0]
        break
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        break
      default: // day
        key = date.toISOString().split('T')[0]
    }
    
    const existing = trendsMap.get(key) || { period: key, sales: 0, amount: 0 }
    existing.sales += 1
    existing.amount += sale.total
    trendsMap.set(key, existing)
  })

  return Array.from(trendsMap.values()).sort((a, b) => a.period.localeCompare(b.period))
}

async function getTopProducts(storeId: string, startDate: Date, limit: number) {
  const topProducts = await prisma.saleItem.groupBy({
    by: ['productId'],
    where: {
      sale: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED'
      }
    },
    _sum: {
      quantity: true,
      totalPrice: true
    },
    _count: true,
    orderBy: {
      _sum: {
        totalPrice: 'desc'
      }
    },
    take: limit
  })

  const productIds = topProducts.map(item => item.productId)
  const products = await prisma.product.findMany({
    where: { id: { in: productIds } },
    select: {
      id: true,
      name: true,
      sku: true,
      sellingPrice: true,
      costPrice: true,
      category: {
        select: { name: true }
      }
    }
  })

  return topProducts.map(item => {
    const product = products.find(p => p.id === item.productId)
    return {
      productId: item.productId,
      productName: product?.name || 'Unknown',
      sku: product?.sku || '',
      category: product?.category.name || '',
      quantitySold: item._sum.quantity || 0,
      totalRevenue: item._sum.totalPrice || 0,
      salesCount: item._count,
      avgPrice: (item._sum.totalPrice || 0) / (item._sum.quantity || 1)
    }
  })
}

async function getTopCategories(storeId: string, startDate: Date) {
  const categoryData = await prisma.saleItem.findMany({
    where: {
      sale: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED'
      }
    },
    include: {
      product: {
        include: {
          category: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }
    }
  })

  const categoryMap = new Map()
  
  categoryData.forEach(item => {
    const categoryName = item.product.category.name
    const existing = categoryMap.get(categoryName) || {
      categoryId: item.product.category.id,
      categoryName,
      quantitySold: 0,
      totalRevenue: 0,
      salesCount: 0
    }
    
    existing.quantitySold += item.quantity
    existing.totalRevenue += item.totalPrice
    existing.salesCount += 1
    
    categoryMap.set(categoryName, existing)
  })

  return Array.from(categoryMap.values())
    .sort((a, b) => b.totalRevenue - a.totalRevenue)
}

async function getPaymentMethodsAnalysis(storeId: string, startDate: Date) {
  const payments = await prisma.payment.groupBy({
    by: ['method'],
    where: {
      storeId,
      createdAt: { gte: startDate }
    },
    _sum: { amount: true },
    _count: true
  })

  const total = payments.reduce((sum, payment) => sum + (payment._sum.amount || 0), 0)

  return payments.map(payment => ({
    method: payment.method,
    amount: payment._sum.amount || 0,
    count: payment._count,
    percentage: total > 0 ? ((payment._sum.amount || 0) / total) * 100 : 0
  }))
}

async function getHourlyAnalysis(storeId: string, startDate: Date) {
  const sales = await prisma.sale.findMany({
    where: {
      storeId,
      createdAt: { gte: startDate },
      status: 'COMPLETED'
    },
    select: {
      createdAt: true,
      total: true
    }
  })

  const hourlyData = Array.from({ length: 24 }, (_, hour) => ({
    hour,
    sales: 0,
    amount: 0
  }))

  sales.forEach(sale => {
    const hour = new Date(sale.createdAt).getHours()
    hourlyData[hour].sales += 1
    hourlyData[hour].amount += sale.total
  })

  return hourlyData
}

async function getCustomerAnalysis(storeId: string, startDate: Date) {
  const [totalCustomers, newCustomers, returningCustomers, topCustomers] = await Promise.all([
    // Total unique customers
    prisma.sale.findMany({
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED',
        customerId: { not: null }
      },
      select: { customerId: true },
      distinct: ['customerId']
    }).then(sales => sales.length),
    
    // New customers (first purchase in period)
    prisma.customer.count({
      where: {
        storeId,
        createdAt: { gte: startDate }
      }
    }),
    
    // Returning customers
    prisma.sale.findMany({
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED',
        customerId: { not: null }
      },
      include: {
        customer: {
          select: {
            createdAt: true
          }
        }
      }
    }).then(sales => {
      const returningCount = sales.filter(sale => 
        sale.customer && sale.customer.createdAt < startDate
      ).length
      return returningCount
    }),
    
    // Top customers by spending
    prisma.sale.groupBy({
      by: ['customerId'],
      where: {
        storeId,
        createdAt: { gte: startDate },
        status: 'COMPLETED',
        customerId: { not: null }
      },
      _sum: { total: true },
      _count: true,
      orderBy: {
        _sum: { total: 'desc' }
      },
      take: 10
    }).then(async (grouped) => {
      const customerIds = grouped.map(g => g.customerId).filter(Boolean)
      const customers = await prisma.customer.findMany({
        where: { id: { in: customerIds } },
        select: { id: true, name: true, phone: true }
      })
      
      return grouped.map(item => {
        const customer = customers.find(c => c.id === item.customerId)
        return {
          customerId: item.customerId,
          customerName: customer?.name || 'Unknown',
          customerPhone: customer?.phone || '',
          totalSpent: item._sum.total || 0,
          purchaseCount: item._count
        }
      })
    })
  ])

  return {
    totalCustomers,
    newCustomers,
    returningCustomers,
    topCustomers
  }
}

async function getComparisonData(storeId: string, currentStartDate: Date, periodDays: number) {
  const previousStartDate = new Date(currentStartDate)
  previousStartDate.setDate(previousStartDate.getDate() - periodDays)
  const previousEndDate = new Date(currentStartDate)

  const [currentPeriod, previousPeriod] = await Promise.all([
    getSalesSummary(storeId, currentStartDate),
    prisma.sale.aggregate({
      where: {
        storeId,
        createdAt: { gte: previousStartDate, lt: previousEndDate },
        status: 'COMPLETED'
      },
      _sum: { total: true },
      _count: true,
      _avg: { total: true }
    })
  ])

  const previousSummary = {
    totalSales: previousPeriod._sum.total || 0,
    salesCount: previousPeriod._count,
    avgOrderValue: previousPeriod._avg.total || 0
  }

  return {
    current: currentPeriod,
    previous: previousSummary,
    growth: {
      salesGrowth: previousSummary.totalSales > 0 
        ? ((currentPeriod.totalSales - previousSummary.totalSales) / previousSummary.totalSales) * 100 
        : 0,
      countGrowth: previousSummary.salesCount > 0 
        ? ((currentPeriod.salesCount - previousSummary.salesCount) / previousSummary.salesCount) * 100 
        : 0,
      avgOrderGrowth: previousSummary.avgOrderValue > 0 
        ? ((currentPeriod.avgOrderValue - previousSummary.avgOrderValue) / previousSummary.avgOrderValue) * 100 
        : 0
    }
  }
}
