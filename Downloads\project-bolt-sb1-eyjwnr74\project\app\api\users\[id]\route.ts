import { NextRequest, NextResponse } from 'next/server'
import {
  withPermission,
  createSuccessResponse,
  createErrorResponse,
  createAuditLog,
  validateStoreAccess
} from '@/lib/api-utils'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import bcrypt from 'bcryptjs'

const userUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Valid email is required').optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  role: z.enum(['FOUNDER', 'SUPER_ADMIN', 'ADMIN', 'STAFF', 'DISTRIBUTOR']).optional(),
  phone: z.string().optional(),
  isActive: z.boolean().optional(),
  permissions: z.array(z.string()).optional()
})

// GET /api/users/[id] - Get single user
export const GET = withPermission('USER', 'READ', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]

  const targetUser = await prisma.user.findFirst({
    where: {
      id,
      storeId
    },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      role: true,
      isActive: true,
      permissions: true,
      lastLoginAt: true,
      createdAt: true,
      updatedAt: true,
      _count: {
        select: {
          sales: true,
          purchases: true,
          auditLogs: true
        }
      },
      sales: {
        select: {
          id: true,
          saleNo: true,
          totalAmount: true,
          status: true,
          createdAt: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      },
      purchases: {
        select: {
          id: true,
          purchaseNo: true,
          totalAmount: true,
          status: true,
          createdAt: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      }
    }
  })

  if (!targetUser) {
    return createErrorResponse('User not found', 404)
  }

  return createSuccessResponse(targetUser)
})

// PUT /api/users/[id] - Update user
export const PUT = withPermission('USER', 'UPDATE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]
  const body = await request.json()
  const data = userUpdateSchema.parse(body)

  // Check if user exists
  const existingUser = await prisma.user.findFirst({
    where: {
      id,
      storeId
    }
  })

  if (!existingUser) {
    return createErrorResponse('User not found', 404)
  }

  // Check if email already exists (if email is being updated)
  if (data.email && data.email !== existingUser.email) {
    const emailExists = await prisma.user.findFirst({
      where: {
        email: data.email,
        storeId,
        id: { not: id }
      }
    })

    if (emailExists) {
      return createErrorResponse('User with this email already exists', 400)
    }
  }

  // Prepare update data
  const updateData: any = {}

  if (data.name) updateData.name = data.name
  if (data.email) updateData.email = data.email
  if (data.role) updateData.role = data.role
  if (data.phone !== undefined) updateData.phone = data.phone
  if (data.isActive !== undefined) updateData.isActive = data.isActive
  if (data.permissions) updateData.permissions = data.permissions

  // Hash password if provided
  if (data.password) {
    updateData.password = await bcrypt.hash(data.password, 12)
  }

  const updatedUser = await prisma.user.update({
    where: { id },
    data: updateData,
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
      role: true,
      isActive: true,
      permissions: true,
      lastLoginAt: true,
      createdAt: true,
      updatedAt: true,
      _count: {
        select: {
          sales: true,
          purchases: true,
          auditLogs: true
        }
      }
    }
  })

  // Create audit log
  const changes = Object.keys(updateData).filter(key => key !== 'password')
  await createAuditLog(
    'UPDATE',
    'USER',
    `Updated user: ${updatedUser.name} (${updatedUser.email}) - Changed: ${changes.join(', ')}`,
    user.id,
    storeId
  )

  return createSuccessResponse(updatedUser, 'User updated successfully')
})

// DELETE /api/users/[id] - Delete user (soft delete)
export const DELETE = withPermission('USER', 'DELETE', async (request: NextRequest, user: any, context?: any) => {
  const storeId = validateStoreAccess(user)
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  const id = pathSegments[pathSegments.length - 1]

  // Check if user exists
  const existingUser = await prisma.user.findFirst({
    where: {
      id,
      storeId
    },
    include: {
      _count: {
        select: {
          sales: true,
          purchases: true
        }
      }
    }
  })

  if (!existingUser) {
    return createErrorResponse('User not found', 404)
  }

  // Prevent deleting yourself
  if (existingUser.id === user.id) {
    return createErrorResponse('Cannot delete your own account', 400)
  }

  // Prevent deleting founder
  if (existingUser.role === 'FOUNDER') {
    return createErrorResponse('Cannot delete founder account', 400)
  }

  // Check if user has transactions
  if (existingUser._count.sales > 0 || existingUser._count.purchases > 0) {
    // Soft delete - deactivate instead of deleting
    await prisma.user.update({
      where: { id },
      data: { isActive: false }
    })

    // Create audit log
    await createAuditLog(
      'DEACTIVATE',
      'USER',
      `Deactivated user: ${existingUser.name} (${existingUser.email}) - Has ${existingUser._count.sales} sales and ${existingUser._count.purchases} purchases`,
      user.id,
      storeId
    )

    return createSuccessResponse(null, 'User deactivated successfully (has transaction history)')
  } else {
    // Hard delete if no transactions
    await prisma.user.delete({
      where: { id }
    })

    // Create audit log
    await createAuditLog(
      'DELETE',
      'USER',
      `Deleted user: ${existingUser.name} (${existingUser.email})`,
      user.id,
      storeId
    )

    return createSuccessResponse(null, 'User deleted successfully')
  }
})


