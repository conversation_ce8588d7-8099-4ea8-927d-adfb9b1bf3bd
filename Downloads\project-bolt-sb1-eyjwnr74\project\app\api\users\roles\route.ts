import { NextRequest } from 'next/server'
import { 
  withPermission, 
  createSuccessResponse
} from '@/lib/api-utils'

// GET /api/users/roles - Get available roles and their permissions
export const GET = withPermission('USER', 'READ', async (request: NextRequest, user: any) => {
  const rolePermissions = {
    FOUNDER: {
      name: 'Founder',
      description: 'Full system access with all permissions',
      permissions: [
        'DASHBOARD:READ',
        'USER:CREATE', 'USER:READ', 'USER:UPDATE', 'USER:DELETE',
        'STORE:CREATE', 'STORE:READ', 'STORE:UPDATE', 'STORE:DELETE',
        'PRODUCT:CREATE', 'PRODUCT:READ', 'PRODUCT:UPDATE', 'PRODUCT:DELETE',
        'CATEGORY:CREATE', 'CATEGORY:READ', 'CATEGORY:UPDATE', 'CATEGORY:DELETE',
        'INVENTORY:READ', 'INVENTORY:UPDATE',
        'SALES:CREATE', 'SALES:READ', 'SALES:UPDATE', 'SALES:DELETE',
        'PURCHASE:CREATE', 'PURCHASE:READ', 'PURCHASE:UPDATE', 'PURCHASE:DELETE',
        'CUSTOMER:CREATE', 'CUSTOMER:READ', 'CUSTOMER:UPDATE', 'CUSTOMER:DELETE',
        'SUPPLIER:CREATE', 'SUPPLIER:READ', 'SUPPLIER:UPDATE', 'SUPPLIER:DELETE',
        'EXPENSE:CREATE', 'EXPENSE:READ', 'EXPENSE:UPDATE', 'EXPENSE:DELETE',
        'REPORTS:READ',
        'SETTINGS:READ', 'SETTINGS:UPDATE',
        'AUDIT:READ'
      ]
    },
    SUPER_ADMIN: {
      name: 'Super Admin',
      description: 'Administrative access with most permissions',
      permissions: [
        'DASHBOARD:READ',
        'USER:CREATE', 'USER:READ', 'USER:UPDATE',
        'STORE:READ', 'STORE:UPDATE',
        'PRODUCT:CREATE', 'PRODUCT:READ', 'PRODUCT:UPDATE', 'PRODUCT:DELETE',
        'CATEGORY:CREATE', 'CATEGORY:READ', 'CATEGORY:UPDATE', 'CATEGORY:DELETE',
        'INVENTORY:READ', 'INVENTORY:UPDATE',
        'SALES:CREATE', 'SALES:READ', 'SALES:UPDATE',
        'PURCHASE:CREATE', 'PURCHASE:READ', 'PURCHASE:UPDATE',
        'CUSTOMER:CREATE', 'CUSTOMER:READ', 'CUSTOMER:UPDATE', 'CUSTOMER:DELETE',
        'SUPPLIER:CREATE', 'SUPPLIER:READ', 'SUPPLIER:UPDATE', 'SUPPLIER:DELETE',
        'EXPENSE:CREATE', 'EXPENSE:READ', 'EXPENSE:UPDATE', 'EXPENSE:DELETE',
        'REPORTS:READ',
        'SETTINGS:READ', 'SETTINGS:UPDATE'
      ]
    },
    ADMIN: {
      name: 'Admin',
      description: 'Store management with operational permissions',
      permissions: [
        'DASHBOARD:READ',
        'USER:READ',
        'PRODUCT:CREATE', 'PRODUCT:READ', 'PRODUCT:UPDATE',
        'CATEGORY:CREATE', 'CATEGORY:READ', 'CATEGORY:UPDATE',
        'INVENTORY:READ', 'INVENTORY:UPDATE',
        'SALES:CREATE', 'SALES:READ', 'SALES:UPDATE',
        'PURCHASE:CREATE', 'PURCHASE:READ', 'PURCHASE:UPDATE',
        'CUSTOMER:CREATE', 'CUSTOMER:READ', 'CUSTOMER:UPDATE',
        'SUPPLIER:CREATE', 'SUPPLIER:READ', 'SUPPLIER:UPDATE',
        'EXPENSE:CREATE', 'EXPENSE:READ', 'EXPENSE:UPDATE',
        'REPORTS:READ'
      ]
    },
    STAFF: {
      name: 'Staff',
      description: 'Basic operational access for daily tasks',
      permissions: [
        'DASHBOARD:READ',
        'PRODUCT:READ',
        'CATEGORY:READ',
        'INVENTORY:READ',
        'SALES:CREATE', 'SALES:READ',
        'CUSTOMER:CREATE', 'CUSTOMER:READ', 'CUSTOMER:UPDATE',
        'REPORTS:READ'
      ]
    },
    DISTRIBUTOR: {
      name: 'Distributor',
      description: 'Limited access for distributor operations',
      permissions: [
        'DASHBOARD:READ',
        'PRODUCT:READ',
        'PURCHASE:CREATE', 'PURCHASE:READ',
        'SUPPLIER:READ',
        'REPORTS:READ'
      ]
    }
  }

  return createSuccessResponse(rolePermissions)
})
